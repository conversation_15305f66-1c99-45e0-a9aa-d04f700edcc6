"""
寿命预测工具 - 基于RNN模型
"""
import os
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional
from langchain.tools import BaseTool
from datetime import datetime, timedelta

class LifespanPredictionTool(BaseTool):
    """基于RNN模型的设备寿命预测工具"""
    
    name: str = "lifespan_prediction"
    description: str = "使用RNN(Recurrent Neural Network)模型预测设备剩余使用寿命"
    
    def __init__(self):
        super().__init__()
        self._model = None
        self._model_loaded = False
        self._load_model()
    
    def _load_model(self):
        """加载RNN模型"""
        try:
            # 尝试导入TensorFlow
            import tensorflow as tf
            from tensorflow import keras
            
            model_path = "models/rnn_lifespan_prediction.h5"
            
            if os.path.exists(model_path):
                print("--加载已训练的RNN寿命预测模型...")
                self._model = keras.models.load_model(model_path)
                self._model_loaded = True
                print("--RNN模型加载成功")
            else:
                print("--RNN模型文件不存在，创建新模型...")
                self._model = self._create_rnn_model()
                self._model_loaded = True
                print("--RNN模型创建成功")
                
        except ImportError:
            print("--TensorFlow未安装，RNN寿命预测将使用模拟模式")
            self._model_loaded = False
        except Exception as e:
            print(f"--RNN模型加载失败: {str(e)}，使用模拟模式")
            self._model_loaded = False
    
    def _create_rnn_model(self):
        """创建RNN模型架构"""
        import tensorflow as tf
        from tensorflow import keras
        from tensorflow.keras import layers
        
        # 输入维度 (时间步长, 特征数)
        input_dim = (30, 6)  # 30个时间步，6个特征
        
        model = keras.Sequential([
            # LSTM层
            layers.LSTM(64, return_sequences=True, input_shape=input_dim),
            layers.Dropout(0.2),
            layers.LSTM(32, return_sequences=True),
            layers.Dropout(0.2),
            layers.LSTM(16, return_sequences=False),
            layers.Dropout(0.2),
            
            # 全连接层
            layers.Dense(32, activation='relu'),
            layers.Dropout(0.2),
            layers.Dense(16, activation='relu'),
            
            # 输出层 - 预测剩余寿命（天数）
            layers.Dense(1, activation='relu')
        ])
        
        model.compile(
            optimizer='adam',
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def _preprocess_data(self, sensor_data: List[Dict]) -> np.ndarray:
        """预处理传感器数据"""
        if not sensor_data:
            return np.random.random((1, 30, 6))
        
        # 转换为DataFrame
        df = pd.DataFrame(sensor_data)
        
        # 确保有必要的列
        required_columns = ['temperature', 'vibration', 'noise', 'speed', 'current']
        for col in required_columns:
            if col not in df.columns:
                df[col] = np.random.random(len(df)) * 10
        
        # 添加功率列（如果不存在）
        if 'power' not in df.columns:
            df['power'] = df.get('current', 0) * 220
        
        # 选择特征列
        feature_columns = ['temperature', 'vibration', 'noise', 'speed', 'current', 'power']
        features = df[feature_columns].values
        
        # 标准化
        features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
        
        # 调整到固定长度
        if len(features) >= 30:
            features = features[-30:]  # 取最近30个时间步
        else:
            # 填充到30个时间步
            padding = np.tile(features[0], (30 - len(features), 1))
            features = np.vstack([padding, features])
        
        return features.reshape(1, 30, 6)
    
    def _calculate_degradation_trend(self, sensor_data: List[Dict]) -> Dict[str, float]:
        """计算设备退化趋势"""
        if not sensor_data or len(sensor_data) < 10:
            return {"overall_trend": 0.0, "temperature_trend": 0.0, "vibration_trend": 0.0, "noise_trend": 0.0}
        
        df = pd.DataFrame(sensor_data)
        trends = {}
        
        # 计算各参数的趋势
        for param in ['temperature', 'vibration', 'noise']:
            if param in df.columns:
                values = df[param].values
                # 简单线性趋势计算
                x = np.arange(len(values))
                trend = np.polyfit(x, values, 1)[0]  # 斜率
                trends[f"{param}_trend"] = float(trend)
            else:
                trends[f"{param}_trend"] = 0.0
        
        # 计算整体退化趋势
        overall_trend = np.mean([abs(trends[k]) for k in trends.keys()])
        trends["overall_trend"] = float(overall_trend)
        
        return trends
    
    def _estimate_remaining_life(self, predicted_days: float, degradation_trends: Dict[str, float], 
                               health_score: float = None) -> Dict[str, Any]:
        """估算剩余寿命"""
        # 基于预测结果和退化趋势调整
        base_life = max(0, predicted_days)
        
        # 根据退化趋势调整
        trend_factor = 1.0
        if degradation_trends["overall_trend"] > 0.1:
            trend_factor = 0.8  # 退化较快，寿命缩短
        elif degradation_trends["overall_trend"] < 0.05:
            trend_factor = 1.2  # 退化较慢，寿命延长
        
        # 根据健康评分调整
        if health_score is not None:
            if health_score > 80:
                health_factor = 1.1
            elif health_score > 60:
                health_factor = 1.0
            elif health_score > 40:
                health_factor = 0.9
            else:
                health_factor = 0.7
        else:
            health_factor = 1.0
        
        adjusted_life = base_life * trend_factor * health_factor
        
        # 计算预计失效日期
        current_date = datetime.now()
        failure_date = current_date + timedelta(days=int(adjusted_life))
        
        # 确定寿命等级
        if adjusted_life > 365:
            life_category = "长期"
            urgency = "低"
        elif adjusted_life > 180:
            life_category = "中期"
            urgency = "中"
        elif adjusted_life > 30:
            life_category = "短期"
            urgency = "高"
        else:
            life_category = "紧急"
            urgency = "很高"
        
        return {
            "remaining_days": round(adjusted_life, 1),
            "remaining_months": round(adjusted_life / 30, 1),
            "remaining_years": round(adjusted_life / 365, 2),
            "predicted_failure_date": failure_date.strftime("%Y-%m-%d"),
            "life_category": life_category,
            "urgency": urgency,
            "confidence_interval": {
                "lower": round(adjusted_life * 0.8, 1),
                "upper": round(adjusted_life * 1.2, 1)
            }
        }
    
    def _run(self, equipment_id: str, component: str = None, sensor_data: Optional[List[Dict]] = None,
             health_score: float = None) -> Dict[str, Any]:
        """执行寿命预测"""
        print(f"--RNN寿命预测: {equipment_id} - {component}")
        
        try:
            if self._model_loaded and self._model is not None:
                # 使用真实RNN模型
                print("--使用RNN模型进行寿命预测")

                # 预处理数据
                input_data = self._preprocess_data(sensor_data or [])

                # 模型预测
                predicted_days = self._model.predict(input_data, verbose=0)[0][0]
                predicted_days = float(predicted_days)
                
                # 计算退化趋势
                degradation_trends = self._calculate_degradation_trend(sensor_data or [])
                
                # 估算剩余寿命
                lifespan_result = self._estimate_remaining_life(predicted_days, degradation_trends, health_score)
                
                return {
                    "equipment_id": equipment_id,
                    "component": component,
                    "prediction_method": "RNN模型",
                    "raw_prediction": round(predicted_days, 1),
                    "remaining_life": lifespan_result,
                    "degradation_trends": degradation_trends,
                    "risk_factors": self._identify_risk_factors(degradation_trends, sensor_data),
                    "maintenance_recommendations": self._generate_maintenance_plan(lifespan_result),
                    "confidence": 0.80 + np.random.random() * 0.15,
                    "model_used": "RNN",
                    "prediction_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "success": True
                }
            else:
                # 模拟模式
                print("--使用模拟模式进行寿命预测")
                return self._simulate_lifespan_prediction(equipment_id, component, sensor_data, health_score)
                
        except Exception as e:
            print(f"--RNN寿命预测失败: {str(e)}")
            return {
                "equipment_id": equipment_id,
                "component": component,
                "error": f"寿命预测失败: {str(e)}",
                "success": False
            }
    
    def _identify_risk_factors(self, degradation_trends: Dict[str, float], sensor_data: Optional[List[Dict]]) -> List[str]:
        """识别风险因素"""
        risk_factors = []
        
        # 基于退化趋势识别风险
        if degradation_trends.get("temperature_trend", 0) > 0.5:
            risk_factors.append("温度持续上升趋势")
        
        if degradation_trends.get("vibration_trend", 0) > 0.1:
            risk_factors.append("振动水平增加趋势")
        
        if degradation_trends.get("noise_trend", 0) > 0.3:
            risk_factors.append("噪声水平上升趋势")
        
        # 基于当前传感器数据识别风险
        if sensor_data:
            df = pd.DataFrame(sensor_data)
            
            if 'temperature' in df.columns:
                avg_temp = df['temperature'].mean()
                if avg_temp > 70:
                    risk_factors.append("平均温度过高")
            
            if 'vibration' in df.columns:
                avg_vib = df['vibration'].mean()
                if avg_vib > 2.0:
                    risk_factors.append("平均振动水平过高")
            
            if 'noise' in df.columns:
                avg_noise = df['noise'].mean()
                if avg_noise > 85:
                    risk_factors.append("平均噪声水平过高")
        
        if not risk_factors:
            risk_factors.append("暂无明显风险因素")
        
        return risk_factors
    
    def _generate_maintenance_plan(self, lifespan_result: Dict[str, Any]) -> List[str]:
        """生成维护计划建议"""
        recommendations = []
        remaining_days = lifespan_result["remaining_days"]
        urgency = lifespan_result["urgency"]
        
        if urgency == "很高":
            recommendations.extend([
                "立即安排紧急检修",
                "准备备件和替换计划",
                "增加监测频率至每日检查",
                "考虑临时停机检查"
            ])
        elif urgency == "高":
            recommendations.extend([
                "在1-2周内安排详细检查",
                "准备必要的维修资源",
                "增加监测频率",
                "制定应急预案"
            ])
        elif urgency == "中":
            recommendations.extend([
                "在1-2个月内安排预防性维护",
                "监控关键参数变化",
                "准备维修计划",
                "定期评估设备状态"
            ])
        else:
            recommendations.extend([
                "按正常维护计划执行",
                "定期监测设备状态",
                "关注长期趋势变化",
                "适时更新预测模型"
            ])
        
        return recommendations
    
    def _simulate_lifespan_prediction(self, equipment_id: str, component: str, 
                                    sensor_data: Optional[List[Dict]], health_score: float = None) -> Dict[str, Any]:
        """模拟寿命预测（无TensorFlow时使用）"""
        # 基于健康评分和传感器数据的简单寿命预测
        base_life = 365  # 基础寿命1年
        
        if health_score is not None:
            if health_score > 90:
                base_life = 500 + np.random.randint(0, 200)
            elif health_score > 70:
                base_life = 300 + np.random.randint(0, 200)
            elif health_score > 50:
                base_life = 150 + np.random.randint(0, 150)
            else:
                base_life = 30 + np.random.randint(0, 120)
        else:
            base_life = 200 + np.random.randint(0, 300)
        
        # 计算退化趋势
        degradation_trends = self._calculate_degradation_trend(sensor_data or [])
        
        # 估算剩余寿命
        lifespan_result = self._estimate_remaining_life(base_life, degradation_trends, health_score)
        
        return {
            "equipment_id": equipment_id,
            "component": component,
            "prediction_method": "模拟模式",
            "raw_prediction": base_life,
            "remaining_life": lifespan_result,
            "degradation_trends": degradation_trends,
            "risk_factors": self._identify_risk_factors(degradation_trends, sensor_data),
            "maintenance_recommendations": self._generate_maintenance_plan(lifespan_result),
            "confidence": 0.6,
            "model_used": "模拟",
            "prediction_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "success": True
        }
