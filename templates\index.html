<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运维大模型与人工智能Agent协同的大型装备智能维护与排故系统</title>
    <!-- 本地CSS和JS文件 - 提高加载速度 -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/all.min.css">
    <script src="/static/js/plotly-2.18.0.min.js"></script>

    <!-- 临时图标-->
    <!-- <style>
        /* 当Font Awesome字体文件缺失时的备用图标 */
        .fas.fa-robot::before {
            content: "🤖";
            font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
        }
        .fas.fa-paper-plane::before {
            content: "📤";
            font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
        }
        .fas.fa-circle::before {
            content: "●";
            font-family: Arial, sans-serif;
        }
        /* 确保图标正常显示 */
        .fas {
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }
    </style> -->
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #5f6368;
            --dark-color: #202124;
            --light-color: #ffffff;
            --border-color: #dadce0;
            --hover-color: #f8f9fa;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: var(--dark-color);
        }

        .header {
            background-color: var(--primary-color);
            color: var(--light-color);
            padding: 1rem 0;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .main-container {
            display: flex;
            gap: 2rem;
            padding: 2rem;
            width: 98%;  /* 使用百分比宽度 */
            margin: 0 auto;
        }

        .chat-section {
            flex: 2;  /* 聊天部分占2份 */
            min-width: 800px;  /* 设置最小宽度 */
            max-width: 1200px;  /* 设置最大宽度 */
            background: var(--light-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px var(--shadow-color);
            display: flex;
            flex-direction: column;
            height: calc(100vh - 150px);
        }

        .analysis-section {
            flex: 3;  /* 分析报告部分占3份 */
            min-width: 1000px;  /* 设置最小宽度 */
            background: var(--light-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px var(--shadow-color);
            padding: 1.5rem;
            height: calc(100vh - 150px);
            overflow-y: auto;
        }

        .analysis-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            text-align: center;
        }

        /* 图表容器宽度 */
        .visualization-section {
            text-align: center;
            margin-bottom: 2rem;
            width: 100%;
        }

        .chart-container {
            margin: 2rem auto;  /* 居中对齐 */
            text-align: center;
            width: 98%;  /* 增加图表宽度 */
        }

        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }

        .chat-message {
            display: flex;
            margin-bottom: 1rem;
            align-items: flex-start;
        }

        .user-message {
            flex-direction: row-reverse;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 1rem;
            flex-shrink: 0;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .user-avatar {
            background-color: var(--primary-color);
            color: var(--light-color);
        }

        .system-avatar {
            background-color: var(--secondary-color);
            color: var(--light-color);
        }

        .avatar i {
            font-size: 1.2rem;
        }

        /* 消息内容样式 */
        .message-content {
            background-color: var(--hover-color);
            padding: 1.2rem;
            border-radius: 8px;
            max-width: 85%;
            white-space: pre-line;  /* pre-line以保持换行但合并多余空格 */
            box-shadow: 0 1px 2px var(--shadow-color);
            font-size: 1.1rem;
            line-height: 1.5;
        }

        /* 列表样式 */
        .message-content ul {
            margin: 0.5rem 0;
            padding-left: 1.2rem;
        }

        .message-content li {
            margin: 0.3rem 0;
        }

        .system-message .message-content {
            background-color: var(--hover-color);
            margin-right: auto;
        }

        .user-message .message-content {
            background-color: var(--primary-color);
            color: var(--light-color);
            margin-left: auto;
        }

        /* 输入框 */
        .input-section {
            padding: 1.5rem;  /* 内边距 */
            border-top: 1px solid var(--border-color);
        }

        .input-container {
            display: flex;
            gap: 1.5rem;  /* 间距 */
            max-width: 95%;  /* 输入区域宽度 */
            margin: 0 auto;  /* 居中对齐 */
        }

        .form-control {
            flex: 1;
            padding: 1rem;  /* 内边距 */
            border: 1px solid var(--border-color);
            border-radius: 6px;  /* 圆角 */
            font-size: 1.1rem;  /* 字体大小 */
        }

        .btn-send {
            background-color: var(--primary-color);
            color: var(--light-color);
            border: none;
            border-radius: 4px;
            padding: 1rem 2rem;  /* 按钮大小 */
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.1rem;  /* 字体大小 */
        }

        .btn-send:hover {
            opacity: 0.9;
        }

        /* 表格布局 */
        .diagnosis-table {
            width: 98%;  /* 表格宽度 */
            margin: 1rem auto;  /* 居中对齐 */
            border-collapse: collapse;
            font-size: 1.1rem;  /* 字体大小 */
        }

        .diagnosis-table th {
            background-color: var(--primary-color);
            color: var(--light-color);
            padding: 1.2rem;  /* 单元格内边距 */
            text-align: left;
            font-weight: 600;
        }

        .diagnosis-table td {
            padding: 1.2rem;  /* 单元格内边距 */
            border-bottom: 1px solid var(--border-color);
        }

        .diagnosis-table tr:hover {
            background-color: var(--hover-color);
        }

        /* 响应式设计 */
        @media (max-width: 1920px) {
            .main-container {
                width: 95%;
            }
            
            .chat-section {
                min-width: 600px;
            }
            
            .analysis-section {
                min-width: 800px;
            }
        }

        @media (max-width: 1440px) {
            .main-container {
                width: 98%;
                gap: 1rem;
            }
            
            .chat-section {
                min-width: 500px;
            }
            
            .analysis-section {
                min-width: 700px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="text-center">运维大模型与人工智能Agent协同的大型装备智能维护与排故系统</h1>
        </div>
    </div>

    <div class="main-container">
        <!-- 聊天界面 -->
        <div class="chat-section">
            <div class="chat-container" id="chatContainer">
                <!-- 欢迎消息 -->
                <div class="chat-message system-message">
                    <div class="avatar system-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">欢迎使用运维大模型与人工智能Agent协同的大型装备智能维护与排故系统！我可以帮您诊断设备故障，请输入您的问题。

例如：
• "3号风机驱动端异响可能原因？"
• "2号风机轴承温度高故障诊断"</div>

                </div>
            </div>
            
            <div class="input-section">
                <!-- 协调器选择 -->
                <div class="coordinator-selection mb-3">
                    <label for="coordinatorSelect" class="form-label">选择AI协调器：</label>
                    <select class="form-select" id="coordinatorSelect">
                        <option value="auto">自动选择</option>
                        <option value="deepseek">DeepSeek智能协调器</option>
                        <option value="simple">简化协调器</option>
                    </select>
                    <small class="text-muted">DeepSeek协调器提供最智能的对话体验</small>
                </div>

                <div class="input-container">
                    <input type="text" class="form-control" id="userInput"
                           placeholder="请输入您的问题（例如：3号风机驱动端异响可能原因？）">
                    <button class="btn btn-send" type="button" id="sendButton">
                        <i class="fas fa-paper-plane me-2"></i>发送
                    </button>
                </div>

                <!-- 状态指示器 -->
                <div class="status-indicator mt-2" id="statusIndicator">
                    <small class="text-muted">
                        <i class="fas fa-circle text-success"></i>
                        <span id="statusText">系统就绪</span>
                    </small>
                </div>
            </div>
        </div>
        
        <!-- 分析报告 -->
        <div class="analysis-section">
            <div class="analysis-title">设备状态分析报告</div>
            
            <!-- 传感器数据可视化 -->
            <div class="visualization-section">
                <div class="chart-container">
                    <div class="chart-title">传感器数据趋势</div>
                    <div id="visualizationPlot"></div>
                </div>
            </div>

            <!-- 诊断结果表格 -->
            <table class="diagnosis-table">
                <tr>
                    <th style="width: 30%">项目</th>
                    <th>详细信息</th>
                </tr>
                <tr>
                    <td>设备信息</td>
                    <td id="equipmentInfo">-</td>
                </tr>
                <tr>
                    <td>可能原因</td>
                    <td id="possibleCauses">-</td>
                </tr>
                <tr>
                    <td>建议措施</td>
                    <td id="recommendations">-</td>
                </tr>
                <tr>
                    <td>相关知识</td>
                    <td id="knowledgeBase">-</td>
                </tr>
                <tr>
                    <td>健康评估</td>
                    <td id="healthAssessment">-</td>
                </tr>
                <tr>
                    <td>寿命预测</td>
                    <td id="lifespanPrediction">-</td>
                </tr>
            </table>
        </div>
    </div>

    <script src="/static/js/bootstrap.bundle.min.js"></script>

    <script>
        let plotlyLoaded = false;

        // 检查Plotly是否加载完成
        function checkPlotlyLoaded() {
            if (typeof Plotly !== 'undefined') {
                plotlyLoaded = true;
                console.log('✅ Plotly已加载');
                return true;
            } else {
                console.log('⚠️ Plotly未加载，等待中...');
                return false;
            }
        }

        // 等待Plotly加载
        function waitForPlotly(callback, maxAttempts = 20) {
            let attempts = 0;
            const checkInterval = setInterval(() => {
                attempts++;
                if (checkPlotlyLoaded()) {
                    clearInterval(checkInterval);
                    callback(true);
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    console.error('❌ Plotly加载超时');
                    callback(false);
                }
            }, 200);
        }

        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chatContainer');
            const userInput = document.getElementById('userInput');
            const sendButton = document.getElementById('sendButton');
            const coordinatorSelect = document.getElementById('coordinatorSelect');
            const statusIndicator = document.getElementById('statusText');

            // 初始化时检查可用的协调器
            async function initializeCoordinators() {
                try {
                    const response = await fetch('/api/coordinators');
                    const data = await response.json();

                    // 更新协调器选项
                    const availableCoordinators = data.available_coordinators;
                    const options = coordinatorSelect.querySelectorAll('option');

                    options.forEach(option => {
                        if (option.value !== 'auto' && !availableCoordinators.includes(option.value)) {
                            option.disabled = true;
                            option.textContent += ' (不可用)';
                        }
                    });

                    // 设置默认选择
                    coordinatorSelect.value = data.default;

                    // 更新状态
                    statusIndicator.textContent = `已加载 ${availableCoordinators.length} 个协调器`;

                } catch (error) {
                    console.error('初始化协调器失败:', error);
                    statusIndicator.textContent = '协调器初始化失败';
                }
            }

            // 格式化诊断结果
            function formatDiagnosisResult(data) {
                let result = '';

                // 显示使用的协调器
                if (data.coordinator_used) {
                    const coordinatorNames = {
                        'deepseek': 'DeepSeek智能协调器',
                        'simple': '简化协调器'
                    };
                    result += `🤖 使用协调器：${coordinatorNames[data.coordinator_used] || data.coordinator_used}\n\n`;
                }

                // 处理DeepSeek协调器的响应
                if (data.coordinator_used === 'deepseek') {
                    // 数据分析结果
                    if (data.data_analysis) {
                        const output = data.data_analysis.agent_output || data.data_analysis.output;
                        if (output) {
                            result += '📊 数据分析：\n';
                            result += output + '\n\n';
                        }
                    }

                    // 故障诊断结果
                    if (data.diagnosis) {
                        const output = data.diagnosis.agent_output || data.diagnosis.output;
                        if (output) {
                            result += '🔧 故障诊断：\n';
                            result += output + '\n\n';
                        }
                    }

                    // 知识推荐
                    if (data.knowledge_recommendations) {
                        const output = data.knowledge_recommendations.agent_output || data.knowledge_recommendations.output;
                        if (output) {
                            result += '📚 知识推荐：\n';
                            result += output + '\n\n';
                        }
                    }

                    // 协调摘要
                    if (data.coordination_summary) {
                        const summary = data.coordination_summary;
                        result += `📈 处理摘要：成功 ${summary.successful_agents}/${summary.total_agents} 个Agent\n`;
                    }

                    // 如果没有具体的Agent输出，显示整体结果
                    if (!result.includes('📊') && !result.includes('🔧') && !result.includes('📚')) {
                        if (data.output) {
                            result += '🤖 AI响应：\n' + data.output + '\n\n';
                        }
                        if (data.error) {
                            result += '❌ 错误信息：\n' + data.error + '\n\n';
                        }
                        // 如果连基本输出都没有，显示调试信息
                        if (!data.output && !data.error) {
                            result += '⚠️ DeepSeek协调器响应异常\n';
                            result += '原始数据: ' + JSON.stringify(data, null, 2).substring(0, 200) + '...\n';
                        }
                    }
                }
                // 处理简化协调器的响应
                else if (data.coordinator_used === 'simple') {
                    // 设备信息
                    if (data.equipment_info) {
                        const info = data.equipment_info;
                        result += `🏭 设备信息：\n`;
                        result += `设备：${info.equipment_id}\n`;
                        if (info.component) result += `组件：${info.component}\n`;
                        if (info.symptoms && info.symptoms.length > 0) {
                            result += `症状：${info.symptoms.join(', ')}\n`;
                        }
                        result += '\n';
                    }

                    // 诊断结果
                    if (data.diagnosis && data.diagnosis.possible_causes) {
                        result += '🔍 可能原因：\n';
                        data.diagnosis.possible_causes.forEach(cause => {
                            result += `• ${cause.description} (严重程度: ${cause.severity})\n`;
                        });
                        result += '\n';
                    }

                    // 建议措施
                    if (data.diagnosis && data.diagnosis.recommendations) {
                        result += '💡 建议措施：\n';
                        data.diagnosis.recommendations.forEach(rec => {
                            result += `• ${rec}\n`;
                        });
                        result += '\n';
                    }

                    // 知识推荐
                    if (data.knowledge_recommendations && data.knowledge_recommendations.relevant_entries) {
                        result += '📖 相关知识：\n';
                        data.knowledge_recommendations.relevant_entries.forEach(entry => {
                            result += `• ${entry.title}\n`;
                        });
                    }
                }

                return result || '处理完成，请查看右侧分析报告。';
            }
            
            function addMessage(message, isUser) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-message ${isUser ? 'user-message' : 'system-message'}`;
                
                const avatar = document.createElement('div');
                avatar.className = `avatar ${isUser ? 'user-avatar' : 'system-avatar'}`;
                
                const icon = document.createElement('i');
                icon.className = isUser ? 'fas fa-user' : 'fas fa-robot';
                avatar.appendChild(icon);
                
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.textContent = message;
                
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);
                chatContainer.appendChild(messageDiv);
                
                // 自动滚动到底部
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
            
            function updateVisualization(sensorData) {
                console.log('更新可视化，传感器数据:', sensorData);

                if (sensorData && sensorData.timestamps) {
                    // 创建Plotly数据格式
                    const traces = [
                        {
                            x: sensorData.timestamps,
                            y: sensorData.vibration,
                            type: 'scatter',
                            mode: 'lines+markers',
                            name: '振动 (mm/s)',
                            line: { color: '#ff6b6b' },
                            yaxis: 'y1'
                        },
                        {
                            x: sensorData.timestamps,
                            y: sensorData.temperature,
                            type: 'scatter',
                            mode: 'lines+markers',
                            name: '温度 (°C)',
                            line: { color: '#4ecdc4' },
                            yaxis: 'y2'
                        },
                        {
                            x: sensorData.timestamps,
                            y: sensorData.noise_level,
                            type: 'scatter',
                            mode: 'lines+markers',
                            name: '噪声 (dB)',
                            line: { color: '#45b7d1' },
                            yaxis: 'y3'
                        }
                    ];

                    const layout = {
                        title: '传感器数据趋势',
                        autosize: true,
                        margin: { t: 50, b: 80, l: 60, r: 60 },
                        height: 400,
                        showlegend: true,
                        legend: {
                            orientation: 'h',
                            y: -0.2
                        },
                        xaxis: {
                            title: '时间',
                            domain: [0, 1]
                        },
                        yaxis: {
                            title: '振动 (mm/s)',
                            titlefont: { color: '#ff6b6b' },
                            tickfont: { color: '#ff6b6b' },
                            side: 'left'
                        },
                        yaxis2: {
                            title: '温度 (°C)',
                            titlefont: { color: '#4ecdc4' },
                            tickfont: { color: '#4ecdc4' },
                            overlaying: 'y',
                            side: 'right'
                        },
                        yaxis3: {
                            title: '噪声 (dB)',
                            titlefont: { color: '#45b7d1' },
                            tickfont: { color: '#45b7d1' },
                            overlaying: 'y',
                            side: 'right',
                            position: 0.95
                        }
                    };

                    // 等待Plotly加载完成后创建图表
                    waitForPlotly((success) => {
                        if (success) {
                            Plotly.newPlot('visualizationPlot', traces, layout, {responsive: true});
                            console.log('✅ 可视化图表已更新');
                        } else {
                            console.error('❌ Plotly加载失败，无法创建图表');
                            document.getElementById('visualizationPlot').innerHTML =
                                '<div class="text-center text-danger p-4">图表加载失败：Plotly库加载超时</div>';
                        }
                    });
                } else {
                    console.log('⚠️ 没有传感器数据，显示占位符');
                    document.getElementById('visualizationPlot').innerHTML =
                        '<div class="text-center text-muted p-4">暂无传感器数据</div>';
                }
            }

            function updateAnalysisReport(data) {
                console.log('更新分析报告，数据:', data);

                // 更新设备信息
                const equipmentInfo = document.getElementById('equipmentInfo');

                // 从查询中提取设备信息
                let equipmentId = '未知设备';
                let component = '未知组件';

                if (data.query) {
                    const match = data.query.match(/(\d+)号?风机/);
                    if (match) {
                        equipmentId = match[1] + '号风机';
                    }

                    if (data.query.includes('驱动端') || data.query.includes('drive')) {
                        component = '驱动端';
                    } else if (data.query.includes('轴承') || data.query.includes('bearing')) {
                        component = '轴承';
                    }
                }

                // 如果有equipment_info，使用它
                if (data.equipment_info) {
                    const info = data.equipment_info;
                    equipmentId = info.equipment_id ? info.equipment_id.replace('fan_', '') + '号风机' : equipmentId;
                    component = info.component || component;
                }

                // 添加工作流信息
                let workflowInfo = '';
                if (data.workflow_info) {
                    const workflowTypes = {
                        'fault_diagnosis': '故障诊断',
                        'health_assessment': '健康评估',
                        'lifespan_prediction': '寿命预测',
                        'comprehensive_analysis': '综合分析',
                        'data_analysis': '数据分析',
                        'knowledge_search': '知识搜索'
                    };
                    const workflowName = workflowTypes[data.workflow_info.workflow_type] || data.workflow_info.workflow_type;
                    workflowInfo = `<br><strong>工作流:</strong> ${workflowName} (置信度: ${(data.workflow_info.confidence * 100).toFixed(1)}%)`;
                }

                equipmentInfo.innerHTML = `
                    <strong>设备:</strong> ${equipmentId}<br>
                    <strong>组件:</strong> ${component}<br>
                    <strong>协调器:</strong> ${data.coordinator_used || '未知'}${workflowInfo}<br>
                    <strong>查询:</strong> ${data.query || '未知'}
                `;

                // 更新可能原因
                const possibleCauses = document.getElementById('possibleCauses');
                let causes = [];

                // 处理不同协调器的数据格式
                if (data.diagnosis && data.diagnosis.possible_causes) {
                    causes = data.diagnosis.possible_causes;
                } else if (data.diagnosis && data.diagnosis.tool_results) {
                    // 从工具结果中提取
                    const toolResults = data.diagnosis.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.possible_causes) {
                            causes = result.possible_causes;
                            break;
                        }
                    }
                }

                // 如果没有找到结构化的原因，尝试从agent_output中解析
                if (causes.length === 0 && data.diagnosis && data.diagnosis.agent_output) {
                    const output = data.diagnosis.agent_output;
                    if (output.includes('故障诊断Agent正在分析中')) {
                        causes = [
                            {description: '系统正在分析中，请稍后查看详细结果', severity: 'unknown'}
                        ];
                    }
                }

                // 如果仍然没有原因，从数据分析中提取异常信息
                if (causes.length === 0 && data.data_analysis && data.data_analysis.tool_results) {
                    const toolResults = data.data_analysis.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.analysis && result.analysis.anomalies) {
                            const anomalies = result.analysis.anomalies;
                            causes = anomalies.map(anomaly => ({
                                description: anomaly,
                                severity: 'medium'
                            }));
                            break;
                        }
                    }
                }

                if (causes.length > 0) {
                    possibleCauses.innerHTML = causes
                        .map(cause => {
                            const severity = cause.severity === 'high' ? '高' :
                                          cause.severity === 'medium' ? '中' :
                                          cause.severity === 'low' ? '低' : '未知';
                            const severityClass = cause.severity === 'high' ? 'text-danger' :
                                                 cause.severity === 'medium' ? 'text-warning' : 'text-info';
                            return `• ${cause.description}（严重程度：<span class="${severityClass}">${severity}</span>）`;
                        })
                        .join('<br>');
                } else {
                    possibleCauses.innerHTML = '暂无诊断结果';
                }

                // 更新建议措施
                const recommendations = document.getElementById('recommendations');
                let recs = [];

                if (data.diagnosis && data.diagnosis.recommendations) {
                    recs = data.diagnosis.recommendations;
                } else if (data.diagnosis && data.diagnosis.tool_results) {
                    // 从工具结果中提取
                    const toolResults = data.diagnosis.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.recommendations) {
                            recs = result.recommendations;
                            break;
                        }
                    }
                }

                // 如果没有找到建议，从知识推荐中提取关键建议
                if (recs.length === 0 && data.knowledge_recommendations && data.knowledge_recommendations.agent_output) {
                    const knowledgeOutput = data.knowledge_recommendations.agent_output;
                    // 提取维护要点
                    const maintenancePoints = knowledgeOutput.match(/维护要点：[\s\S]*?(?=预防措施：|实用技巧：|建议|$)/);
                    if (maintenancePoints) {
                        const points = maintenancePoints[0].match(/\d+\.\s*[^：]+：[^1-9]*/g);
                        if (points) {
                            recs = points.slice(0, 3).map(point => point.replace(/\d+\.\s*/, '').split('：')[0]);
                        }
                    }

                    // 如果还没有，添加通用建议
                    if (recs.length === 0) {
                        recs = [
                            '检查设备润滑状态',
                            '监测振动和噪声水平',
                            '安排专业人员详细检查'
                        ];
                    }
                }

                if (recs.length > 0) {
                    recommendations.innerHTML = recs
                        .map(rec => `• ${rec}`)
                        .join('<br>');
                } else {
                    recommendations.innerHTML = '暂无建议措施';
                }

                // 更新知识库信息
                const knowledgeBase = document.getElementById('knowledgeBase');
                let knowledgeContent = '';

                // 首先尝试从knowledge_recommendations的agent_output中获取
                if (data.knowledge_recommendations && data.knowledge_recommendations.agent_output) {
                    const knowledgeOutput = data.knowledge_recommendations.agent_output;
                    console.log('找到知识推荐输出:', knowledgeOutput.length, '字符');

                    // 提取关键维护知识点
                    const sections = [];

                    // 提取维护要点
                    const maintenanceMatch = knowledgeOutput.match(/维护要点：([\s\S]*?)(?=预防措施：|实用技巧：|建议|$)/);
                    if (maintenanceMatch) {
                        sections.push({
                            title: '维护要点',
                            content: maintenanceMatch[1].trim().substring(0, 200) + '...'
                        });
                    }

                    // 提取预防措施
                    const preventionMatch = knowledgeOutput.match(/预防措施：([\s\S]*?)(?=实用技巧：|建议|$)/);
                    if (preventionMatch) {
                        sections.push({
                            title: '预防措施',
                            content: preventionMatch[1].trim().substring(0, 200) + '...'
                        });
                    }

                    // 提取实用技巧
                    const tipsMatch = knowledgeOutput.match(/实用技巧：([\s\S]*?)(?=建议|$)/);
                    if (tipsMatch) {
                        sections.push({
                            title: '实用技巧',
                            content: tipsMatch[1].trim().substring(0, 200) + '...'
                        });
                    }

                    // 如果没有找到结构化内容，显示前300字符
                    if (sections.length === 0) {
                        sections.push({
                            title: '维护知识',
                            content: knowledgeOutput.substring(0, 300) + '...'
                        });
                    }

                    knowledgeContent = sections
                        .map(section => `
                            <strong>${section.title}</strong><br>
                            <small class="text-muted">${section.content}</small>
                        `)
                        .join('<br><br>');
                }

                // 如果没有从agent_output获取到内容，尝试从tool_results获取
                if (!knowledgeContent && data.knowledge_recommendations && data.knowledge_recommendations.tool_results) {
                    const toolResults = data.knowledge_recommendations.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.relevant_entries) {
                            const knowledge = result.relevant_entries;
                            knowledgeContent = knowledge
                                .map(entry => `
                                    <strong>${entry.title}</strong><br>
                                    <small class="text-muted">${entry.content || '暂无详细内容'}</small>
                                `)
                                .join('<br><br>');
                            break;
                        }
                    }
                }

                // 显示知识内容
                if (knowledgeContent) {
                    knowledgeBase.innerHTML = knowledgeContent;
                } else {
                    knowledgeBase.innerHTML = '暂无相关知识';
                }

                // 更新健康评估
                updateHealthAssessment(data);

                // 更新寿命预测
                updateLifespanPrediction(data);

                // 生成传感器数据可视化
                generateSensorVisualization(data);
            }

            function generateSensorVisualization(data) {
                console.log('生成传感器可视化，数据:', data);

                let sensorData = {
                    vibration: [],
                    temperature: [],
                    noise_level: [],
                    timestamps: []
                };

                // 首先尝试从实际的传感器数据中提取
                let realSensorData = null;

                // 从数据分析结果中提取传感器数据
                if (data.data_analysis && data.data_analysis.tool_results) {
                    const toolResults = data.data_analysis.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.sensor_data && Array.isArray(result.sensor_data)) {
                            realSensorData = result.sensor_data;
                            console.log('✅ 找到实际传感器数据:', realSensorData.length, '条记录');
                            break;
                        }
                    }
                }

                // 如果没有找到传感器数据，尝试从分析结果中提取当前值
                if ((!realSensorData || realSensorData.length === 0) && data.data_analysis && data.data_analysis.tool_results) {
                    const toolResults = data.data_analysis.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.analysis) {
                            const analysis = result.analysis;
                            // 创建基于当前值的模拟数据
                            const currentValues = {
                                temperature: analysis.temperature ? analysis.temperature.current : 45,
                                vibration: analysis.vibration ? analysis.vibration.current : 0.15,
                                // 处理噪声字段的不同命名
                                noise_level: (analysis.noise_level ? analysis.noise_level.current :
                                            (analysis.noise ? analysis.noise.current : 65))
                            };

                            // 生成基于当前值的时间序列数据
                            realSensorData = [];
                            const now = new Date();
                            for (let i = 23; i >= 0; i--) {
                                const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                                realSensorData.push({
                                    timestamp: time.toISOString(),
                                    temperature: currentValues.temperature + (Math.random() - 0.5) * 4,
                                    vibration: currentValues.vibration + (Math.random() - 0.5) * 0.04,
                                    noise_level: currentValues.noise_level + (Math.random() - 0.5) * 6
                                });
                            }
                            console.log('✅ 基于分析结果生成传感器数据:', realSensorData.length, '条记录');
                            break;
                        }
                    }
                }

                if (realSensorData && realSensorData.length > 0) {
                    // 使用实际的传感器数据
                    console.log('📊 使用实际传感器数据，数据条数:', realSensorData.length);
                    console.log('📊 数据来源: 实际传感器数据');

                    // 确保sensorData数组为空，避免数据混合
                    sensorData = {
                        timestamps: [],
                        vibration: [],
                        temperature: [],
                        noise_level: []
                    };

                    // 先按时间戳排序，确保数据顺序正确
                    const sortedData = realSensorData.slice().sort((a, b) => {
                        const timeA = new Date(a.timestamp);
                        const timeB = new Date(b.timestamp);
                        return timeA - timeB;
                    });

                    // 限制数据数量为48个，匹配横轴刻度数量
                    const maxDataPoints = 48;
                    const limitedData = sortedData.slice(0, maxDataPoints);

                    console.log('📊 数据排序完成');
                    console.log('📊 数据限制:', `${sortedData.length} -> ${limitedData.length} 个数据点`);

                    limitedData.forEach((record, index) => {
                        if (record.timestamp) {
                            try {
                                const time = new Date(record.timestamp);
                                if (isNaN(time.getTime())) {
                                    console.warn(`⚠️ 无效时间戳 [${index}]:`, record.timestamp);
                                    sensorData.timestamps.push('--:--');
                                } else {
                                    sensorData.timestamps.push(time.toLocaleTimeString('zh-CN', {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    }));
                                }
                            } catch (e) {
                                console.warn(`⚠️ 时间戳解析错误 [${index}]:`, record.timestamp, e);
                                sensorData.timestamps.push('--:--');
                            }
                        } else {
                            console.warn(`⚠️ 缺失时间戳 [${index}]:`, record);
                            sensorData.timestamps.push('--:--');
                        }

                        // 数据验证和转换
                        const vibration = parseFloat(record.vibration || 0);
                        const temperature = parseFloat(record.temperature || 0);
                        const noiseValue = record.noise_level || record.noise || 0;
                        const noise = parseFloat(noiseValue);

                        sensorData.vibration.push(vibration.toFixed(3));
                        sensorData.temperature.push(temperature.toFixed(1));
                        sensorData.noise_level.push(noise.toFixed(1));

                        // 调试前几条数据
                        if (index < 3) {
                            console.log(`📊 数据 [${index}]:`, {
                                timestamp: record.timestamp,
                                temperature: temperature,
                                vibration: vibration,
                                noise: noise
                            });
                        }
                    });
                } else {
                    // 生成模拟数据
                    console.log('🔄 生成模拟传感器数据');
                    console.log('📊 数据来源: 模拟数据生成');

                    // 确保sensorData数组为空，避免数据混合
                    sensorData = {
                        timestamps: [],
                        vibration: [],
                        temperature: [],
                        noise_level: []
                    };
                    const now = new Date();
                    for (let i = 23; i >= 0; i--) {
                        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                        sensorData.timestamps.push(time.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}));

                        // 根据诊断结果调整数据
                        let vibrationBase = 0.15;
                        let temperatureBase = 45;
                        let noiseBase = 65;

                        // 如果有异常，增加相应的数值
                        if (data.equipment_info && data.equipment_info.symptoms) {
                            const symptoms = data.equipment_info.symptoms;
                            if (symptoms.includes('异响') || symptoms.includes('噪声')) {
                                noiseBase += Math.random() * 10 + 5;
                            }
                            if (symptoms.includes('振动')) {
                                vibrationBase += Math.random() * 0.1 + 0.05;
                            }
                            if (symptoms.includes('温度') || symptoms.includes('发热')) {
                                temperatureBase += Math.random() * 15 + 10;
                            }
                        }

                        sensorData.vibration.push((vibrationBase + (Math.random() - 0.5) * 0.04).toFixed(3));
                        sensorData.temperature.push((temperatureBase + (Math.random() - 0.5) * 4).toFixed(1));
                        sensorData.noise_level.push((noiseBase + (Math.random() - 0.5) * 6).toFixed(1));
                    }
                }

                console.log('📈 传感器数据准备完成，数据点数:', sensorData.timestamps.length);

                // 验证数据一致性
                const dataLengths = {
                    timestamps: sensorData.timestamps.length,
                    temperature: sensorData.temperature.length,
                    vibration: sensorData.vibration.length,
                    noise_level: sensorData.noise_level.length
                };
                console.log('📊 数据长度验证:', dataLengths);

                const allLengths = Object.values(dataLengths);
                if (new Set(allLengths).size !== 1) {
                    console.warn('⚠️ 数据长度不一致!', dataLengths);
                } else {
                    console.log('✅ 数据长度一致，共', allLengths[0], '个数据点');
                }

                updateVisualization(sensorData);
            }
            
            async function sendMessage() {
                const message = userInput.value.trim();
                if (!message) return;

                const selectedCoordinator = coordinatorSelect.value;

                // 添加用户消息
                addMessage(message, true);
                userInput.value = '';

                // 更新状态
                statusIndicator.textContent = '处理中...';
                sendButton.disabled = true;

                try {
                    // 发送请求到后端
                    const response = await fetch('/api/diagnose', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            query: message,
                            coordinator: selectedCoordinator
                        })
                    });

                    const data = await response.json();

                    if (data.error) {
                        addMessage(`❌ 错误：${data.error}`, false);
                        if (data.available) {
                            addMessage(`可用协调器：${data.available.join(', ')}`, false);
                        }
                        return;
                    }

                    // 添加系统响应
                    const systemResponse = formatDiagnosisResult(data);
                    addMessage(systemResponse, false);

                    // 更新分析报告（包含传感器可视化）
                    updateAnalysisReport(data);

                    // 注意：传感器可视化已在updateAnalysisReport中处理，无需重复调用

                    // 更新状态
                    statusIndicator.textContent = `处理完成 - 使用${data.coordinator_used || '未知'}协调器`;

                } catch (error) {
                    addMessage(`🚨 系统错误：${error.message}`, false);
                    statusIndicator.textContent = '处理失败';
                } finally {
                    sendButton.disabled = false;
                }
            }
            
            // 事件监听器
            sendButton.addEventListener('click', sendMessage);
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // 协调器选择变化时更新状态
            coordinatorSelect.addEventListener('change', function() {
                const selected = this.value;
                const coordinatorNames = {
                    'auto': '自动选择',
                    'deepseek': 'DeepSeek智能协调器',
                    'simple': '简化协调器'
                };
                statusIndicator.textContent = `已选择：${coordinatorNames[selected]}`;
            });

            // 初始化
            initializeCoordinators();

            function updateHealthAssessment(data) {
                const healthAssessment = document.getElementById('healthAssessment');
                let healthContent = '-';

                // 从健康分析结果中提取信息
                if (data.health_analysis && data.health_analysis.health_summary) {
                    const summary = data.health_analysis.health_summary;
                    const score = summary.health_score || 0;
                    const status = summary.health_status || '未知';
                    const riskLevel = summary.risk_level || '未知';

                    // 根据健康评分设置颜色
                    let scoreColor = 'text-success';
                    if (score < 50) scoreColor = 'text-danger';
                    else if (score < 70) scoreColor = 'text-warning';

                    healthContent = `
                        <div class="health-assessment">
                            <div class="mb-2">
                                <strong>健康评分:</strong>
                                <span class="${scoreColor}">${score}/100</span>
                                <span class="badge bg-secondary ms-2">${status}</span>
                            </div>
                            <div class="mb-2">
                                <strong>风险等级:</strong>
                                <span class="badge ${riskLevel === '低' ? 'bg-success' : riskLevel === '中' ? 'bg-warning' : 'bg-danger'}">${riskLevel}</span>
                            </div>
                        </div>
                    `;
                } else if (data.health_analysis && data.health_analysis.tool_results) {
                    // 从工具结果中提取健康评估信息
                    const toolResults = data.health_analysis.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.health_score !== undefined) {
                            const score = result.health_score;
                            const status = result.health_status || '未知';

                            let scoreColor = 'text-success';
                            if (score < 50) scoreColor = 'text-danger';
                            else if (score < 70) scoreColor = 'text-warning';

                            healthContent = `
                                <div class="health-assessment">
                                    <div class="mb-2">
                                        <strong>健康评分:</strong>
                                        <span class="${scoreColor}">${score}/100</span>
                                        <span class="badge bg-secondary ms-2">${status}</span>
                                    </div>
                                    <div class="mb-1">
                                        <small class="text-muted">评估方法: ${result.assessment_method || 'CAE模型'}</small>
                                    </div>
                                </div>
                            `;
                            break;
                        }
                    }
                }

                healthAssessment.innerHTML = healthContent;
            }

            function updateLifespanPrediction(data) {
                const lifespanPrediction = document.getElementById('lifespanPrediction');
                let lifespanContent = '-';

                // 从寿命分析结果中提取信息
                if (data.lifespan_analysis && data.lifespan_analysis.lifespan_summary) {
                    const summary = data.lifespan_analysis.lifespan_summary;
                    const remainingDays = summary.remaining_days || 0;
                    const remainingMonths = summary.remaining_months || 0;
                    const failureDate = summary.failure_date || '未知';
                    const urgency = summary.urgency || '未知';

                    // 根据紧急程度设置颜色
                    let urgencyColor = 'bg-success';
                    if (urgency === '很高') urgencyColor = 'bg-danger';
                    else if (urgency === '高') urgencyColor = 'bg-warning';
                    else if (urgency === '中') urgencyColor = 'bg-info';

                    lifespanContent = `
                        <div class="lifespan-prediction">
                            <div class="mb-2">
                                <strong>剩余寿命:</strong>
                                ${remainingDays.toFixed(1)} 天 (${remainingMonths.toFixed(1)} 个月)
                            </div>
                            <div class="mb-2">
                                <strong>预计失效:</strong> ${failureDate}
                            </div>
                            <div class="mb-2">
                                <strong>紧急程度:</strong>
                                <span class="badge ${urgencyColor}">${urgency}</span>
                            </div>
                        </div>
                    `;
                } else if (data.lifespan_analysis && data.lifespan_analysis.tool_results) {
                    // 从工具结果中提取寿命预测信息
                    const toolResults = data.lifespan_analysis.tool_results;
                    for (const [toolName, result] of Object.entries(toolResults)) {
                        if (result.remaining_life) {
                            const remainingLife = result.remaining_life;
                            const days = remainingLife.remaining_days || 0;
                            const months = remainingLife.remaining_months || 0;

                            lifespanContent = `
                                <div class="lifespan-prediction">
                                    <div class="mb-2">
                                        <strong>剩余寿命:</strong>
                                        ${days.toFixed(1)} 天 (${months.toFixed(1)} 个月)
                                    </div>
                                    <div class="mb-1">
                                        <small class="text-muted">预测方法: ${result.prediction_method || 'RNN模型'}</small>
                                    </div>
                                </div>
                            `;
                            break;
                        }
                    }
                }

                lifespanPrediction.innerHTML = lifespanContent;
            }
        });
    </script>
</body>
</html>