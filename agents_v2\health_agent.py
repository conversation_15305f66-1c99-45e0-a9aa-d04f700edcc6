"""
健康评估Agent - 基于CAE模型评估设备健康状态
"""
from typing import Dict, Any, List
from .base_deepseek_agent import DeepSeekAgent
from tools.health_assessment_tools import HealthAssessmentTool

class DeepSeekHealthAgent(DeepSeekAgent):
    """设备健康评估Agent"""
    
    def __init__(self):
        # 初始化健康评估工具
        tools = [HealthAssessmentTool()]
        
        super().__init__(
            name="DeepSeekHealthAgent",
            description="专门负责设备健康状态评估的智能Agent，使用CAE模型分析设备健康状况",
            tools=tools
        )
        print("--DeepSeekHealthAgent 健康评估工具初始化成功")
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的设备健康评估专家。你的主要职责是：

1. 分析设备传感器数据，评估设备健康状态
2. 使用CAE(卷积自编码器)模型进行深度健康分析
3. 提供健康评分、风险等级和维护建议
4. 识别设备异常模式和潜在问题

分析要点：
- 重点关注传感器数据的异常模式
- 评估设备的整体健康状况
- 提供具体的健康评分和风险等级
- 给出针对性的维护建议

请用专业、准确的中文回答，重点突出健康评估结果和建议措施。"""
    
    def _should_use_tool(self, tool, input_data: str) -> bool:
        """判断是否应该使用特定工具"""
        tool_name = tool.__class__.__name__
        
        # 健康评估相关关键词
        health_keywords = [
            "健康", "状态", "评估", "健康评估", "设备状态", 
            "健康状况", "设备健康", "状态分析", "健康分析"
        ]
        
        if "HealthAssessmentTool" in tool_name:
            return any(keyword in input_data for keyword in health_keywords)
        
        return False
    
    def _extract_health_context(self, input_data: str) -> Dict[str, Any]:
        """提取健康评估相关的上下文信息"""
        context = {}
        
        # 提取评估类型
        if "详细" in input_data or "全面" in input_data:
            context["assessment_type"] = "comprehensive"
        elif "快速" in input_data or "简单" in input_data:
            context["assessment_type"] = "quick"
        else:
            context["assessment_type"] = "standard"
        
        # 提取关注重点
        focus_areas = []
        if "温度" in input_data:
            focus_areas.append("temperature")
        if "振动" in input_data:
            focus_areas.append("vibration")
        if "噪声" in input_data or "噪音" in input_data:
            focus_areas.append("noise")
        if "电流" in input_data:
            focus_areas.append("current")
        
        if focus_areas:
            context["focus_areas"] = focus_areas
        
        return context
    
    def _call_tool(self, tool, input_data: str, context: Dict = None) -> Dict[str, Any]:
        """调用工具的统一接口"""
        tool_name = tool.__class__.__name__
        
        if "HealthAssessmentTool" in tool_name:
            # 健康评估工具调用
            equipment_id = self._extract_equipment_id(input_data)
            component = self._extract_component(input_data)
            
            # 从上下文获取传感器数据
            sensor_data = None
            if context and 'sensor_data' in context:
                sensor_data = context['sensor_data']
            elif context and 'tool_results' in context:
                # 尝试从其他工具结果中获取传感器数据
                for tool_result in context['tool_results'].values():
                    if isinstance(tool_result, dict) and 'sensor_data' in tool_result:
                        sensor_data = tool_result['sensor_data']
                        break
            
            return tool._run(
                equipment_id=equipment_id,
                component=component,
                sensor_data=sensor_data
            )
        
        # 默认调用
        return tool._run(input_data)
    
    def _generate_comprehensive_response(self, llm_response: str, tool_results: Dict[str, Any], 
                                       input_data: str) -> Dict[str, Any]:
        """生成综合响应"""
        # 提取健康评估结果
        health_result = None
        for tool_name, result in tool_results.items():
            if "HealthAssessmentTool" in tool_name and isinstance(result, dict) and result.get('success'):
                health_result = result
                break
        
        if health_result:
            # 生成健康评估报告
            health_score = health_result.get('health_score', 0)
            health_status = health_result.get('health_status', '未知')
            risk_level = health_result.get('risk_level', '未知')
            
            # 构建详细的健康分析
            analysis_parts = [
                f"根据CAE模型分析，设备 {health_result.get('equipment_id', '')} 的 {health_result.get('component', '')} 组件健康评估结果如下：",
                f"",
                f"🏥 **健康评分**: {health_score}/100 ({health_status})",
                f"⚠️ **风险等级**: {risk_level}",
                f"🔧 **评估方法**: {health_result.get('assessment_method', 'CAE模型')}",
                f"📊 **置信度**: {health_result.get('confidence', 0):.2%}",
            ]
            
            # 添加特征重要性分析
            if 'feature_importance' in health_result:
                analysis_parts.extend([
                    f"",
                    f"📈 **关键参数分析**:",
                ])
                for feature, importance in health_result['feature_importance'].items():
                    feature_name = {
                        'temperature': '温度',
                        'vibration': '振动',
                        'noise': '噪声',
                        'speed': '转速',
                        'current': '电流',
                        'power': '功率'
                    }.get(feature, feature)
                    analysis_parts.append(f"  - {feature_name}: {importance:.3f}")
            
            # 添加维护建议
            if 'recommendations' in health_result:
                analysis_parts.extend([
                    f"",
                    f"💡 **维护建议**:",
                ])
                for i, rec in enumerate(health_result['recommendations'], 1):
                    analysis_parts.append(f"  {i}. {rec}")
            
            comprehensive_analysis = "\n".join(analysis_parts)
            
            return {
                "agent_output": comprehensive_analysis,
                "tool_results": tool_results,
                "health_summary": {
                    "health_score": health_score,
                    "health_status": health_status,
                    "risk_level": risk_level,
                    "equipment_id": health_result.get('equipment_id'),
                    "component": health_result.get('component')
                },
                "success": True
            }
        else:
            # 没有成功的健康评估结果
            error_msg = "健康评估过程中遇到问题，请检查设备数据或稍后重试。"
            
            return {
                "agent_output": error_msg,
                "tool_results": tool_results,
                "success": False,
                "error": "健康评估工具调用失败"
            }
    
    def process(self, input_data: str, context: Dict = None) -> Dict[str, Any]:
        """处理健康评估请求"""
        print(f"--DeepSeekHealthAgent 开始处理健康评估请求")
        
        try:
            # 提取健康评估上下文
            health_context = self._extract_health_context(input_data)
            if context:
                context.update(health_context)
            else:
                context = health_context
            
            # 调用父类的处理方法
            result = super().process(input_data, context)
            
            print(f"--DeepSeekHealthAgent 健康评估完成")
            return result
            
        except Exception as e:
            print(f"--DeepSeekHealthAgent 处理失败: {str(e)}")
            return {
                "agent_output": f"健康评估Agent处理失败: {str(e)}",
                "tool_results": {},
                "success": False,
                "error": str(e)
            }
