"""
基于DeepSeek的多Agent协调器 - 支持灵活工作流
"""
import time
import json
from typing import Dict, Any, List, Optional
from .data_agent import DeepSeekDataAgent
from .diagnosis_agent import DeepSeekDiagnosisAgent
from .knowledge_agent import DeepSeekKnowledgeAgent
from .health_agent import DeepSeekHealthAgent
from .lifespan_agent import DeepSeekLifespanAgent
from .workflow_manager import WorkflowManager


class DeepSeekMultiAgentCoordinator:
    """基于DeepSeek的多Agent协调器"""
    
    def __init__(self):
        """初始化协调器"""
        try:
            # 初始化工作流管理器
            self.workflow_manager = WorkflowManager()

            # 注册所有可用的Agent
            self.workflow_manager.register_agent("data", DeepSeekDataAgent)
            self.workflow_manager.register_agent("diagnosis", DeepSeekDiagnosisAgent)
            self.workflow_manager.register_agent("knowledge", DeepSeekKnowledgeAgent)
            self.workflow_manager.register_agent("health", DeepSeekHealthAgent)
            self.workflow_manager.register_agent("lifespan", DeepSeekLifespanAgent)

            # 协调历史
            self.coordination_history = []

            print("DeepSeek多Agent协调器初始化成功")
            print("--支持的工作流类型: 故障诊断、健康评估、寿命预测、综合分析、数据分析、知识搜索")

        except Exception as e:
            print(f"协调器初始化失败: {str(e)}")
            # 如果初始化失败，使用简化版本
            self.data_agent = None
            self.diagnosis_agent = None
            self.knowledge_agent = None
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """处理用户查询的主要方法 - 使用灵活工作流"""
        try:
            start_time = time.time()

            # 检查工作流管理器是否可用
            if not hasattr(self, 'workflow_manager') or self.workflow_manager is None:
                return self._fallback_process(query)

            # 1. 分析用户意图，确定工作流类型
            workflow_type, confidence = self.workflow_manager.analyze_user_intent(query)
            print(f"--识别工作流类型: {workflow_type} (置信度: {confidence:.2f})")

            # 2. 生成工作流执行计划
            workflow = self.workflow_manager.generate_workflow(workflow_type, query)
            print(f"--生成工作流: {workflow['description']}")

            # 3. 执行工作流
            workflow_result = self.workflow_manager.execute_workflow(workflow)

            # 4. 检查工作流执行结果
            if not workflow_result.get('success'):
                print("工作流执行失败，回退到简化协调器...")
                return self._fallback_process(query)

            # 5. 整合工作流结果
            final_result = self._integrate_workflow_results(workflow_result, query)

            # 6. 记录处理时间
            processing_time = time.time() - start_time
            final_result['processing_time'] = f"{processing_time:.2f}秒"
            final_result['workflow_info'] = {
                'workflow_type': workflow_type,
                'confidence': confidence,
                'workflow_id': workflow['workflow_id']
            }

            # 7. 记录协调历史
            self._record_coordination(query, final_result)

            # 8. 生成用户友好的响应
            user_friendly_result = self._generate_user_friendly_response(final_result)

            return user_friendly_result

        except Exception as e:
            return self._create_error_response(f"处理查询时发生错误: {str(e)}")

    def _integrate_workflow_results(self, workflow_result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """整合工作流执行结果"""
        print("--整合工作流结果")

        workflow_summary = workflow_result.get('workflow_summary', {})
        integrated_results = workflow_result.get('integrated_results', {})
        all_tool_results = workflow_result.get('all_tool_results', {})

        # 构建最终结果
        final_result = {
            'success': True,
            'overall_status': 'success',
            'workflow_type': workflow_summary.get('workflow_type', 'unknown'),
            'execution_status': workflow_summary.get('execution_status', 'unknown'),
            'success_rate': workflow_summary.get('success_rate', 0),
        }

        # 根据工作流类型整合特定结果
        if 'data_analysis' in integrated_results:
            data_result = integrated_results['data_analysis']
            if data_result.get('success') and 'tool_results' in data_result:
                final_result['data_analysis'] = {
                    'tool_results': data_result['tool_results']
                }

        if 'fault_diagnosis' in integrated_results:
            diagnosis_result = integrated_results['fault_diagnosis']
            if diagnosis_result.get('success'):
                final_result['diagnosis_analysis'] = diagnosis_result

        if 'health_assessment' in integrated_results:
            health_result = integrated_results['health_assessment']
            if health_result.get('success'):
                final_result['health_analysis'] = health_result

        if 'lifespan_prediction' in integrated_results:
            lifespan_result = integrated_results['lifespan_prediction']
            if lifespan_result.get('success'):
                final_result['lifespan_analysis'] = lifespan_result

        if 'knowledge_recommendations' in integrated_results:
            knowledge_result = integrated_results['knowledge_recommendations']
            if knowledge_result.get('success'):
                final_result['knowledge_analysis'] = knowledge_result

        # 如果没有任何成功的结果，标记为失败
        analysis_keys = ['data_analysis', 'diagnosis_analysis', 'health_analysis',
                        'lifespan_analysis', 'knowledge_analysis']
        if not any(key in final_result for key in analysis_keys):
            final_result['overall_status'] = 'failed'
            final_result['success'] = False

        return final_result

    def _agents_available(self) -> bool:
        """检查Agent是否可用"""
        return hasattr(self, 'workflow_manager') and self.workflow_manager is not None
    
    def _fallback_process(self, query: str) -> Dict[str, Any]:
        """回退处理方法"""
        from .simple_coordinator import SimpleMultiAgentCoordinator

        print("使用简化版本处理查询...")
        simple_coordinator = SimpleMultiAgentCoordinator()
        result = simple_coordinator.process_query(query)
        result['mode'] = 'fallback'
        result['note'] = 'DeepSeek API不可用，使用简化版本处理'
        return result
    
    def _parse_query(self, query: str) -> Dict[str, Any]:
        """使用大模型智能解析用户查询"""
        try:
            # 使用DeepSeek API解析查询
            parse_result = self._llm_parse_query(query)
            if parse_result:
                # 保存解析结果供后续使用
                self._last_parse_result = parse_result
                return parse_result
        except Exception as e:
            print(f"--大模型解析失败，使用备用方法: {str(e)}")

        # 备用：简单关键词匹配
        fallback_result = self._fallback_parse_query(query)
        self._last_parse_result = fallback_result
        return fallback_result

    def _llm_parse_query(self, query: str) -> Dict[str, Any]:
        """使用大模型解析查询"""
        from utils.llm_utils import call_deepseek_api

        system_prompt = """你是一个设备故障诊断查询解析专家。请分析用户的查询，提取以下信息：

1. 设备ID：识别设备编号（如：1号风机→fan_1，2号风机→fan_2，fan_3→fan_3等）
2. 组件：识别设备组件（bearing轴承、drive驱动/传动、motor电机、gearbox齿轮箱等）
3. 症状：识别故障症状（异响、温度异常、振动异常、漏油、磨损等）
4. 诊断方法：识别用户指定的诊断方法（CNN/深度学习、传统诊断、模式匹配、混合诊断等）

请以JSON格式返回结果，格式如下：
{
    "equipment_id": "fan_1",
    "component": "drive",
    "symptoms": ["异响", "振动异常"],
    "diagnosis_method": "CNN诊断",
    "confidence": 0.9
}

如果某项信息不明确，请设为null。confidence表示解析的置信度(0-1)。"""

        user_prompt = f"请解析以下设备故障查询：\n\n{query}"

        try:
            response = call_deepseek_api(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1  # 低温度确保结果稳定
            )

            # 解析JSON响应
            import json
            import re

            # 提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())

                # 转换为标准格式
                equipment_info = {
                    'equipment_id': result.get('equipment_id'),
                    'component': result.get('component'),
                    'symptom': result.get('symptoms', [None])[0] if result.get('symptoms') else None,
                    'symptoms': result.get('symptoms', []),
                    'sensor_type': None,
                    'diagnosis_method': result.get('diagnosis_method'),
                    'confidence': result.get('confidence', 0.8),
                    'llm_parsed': True
                }

                print(f"》》大模型解析结果: {equipment_info}")
                return equipment_info

        except Exception as e:
            print(f"--大模型解析异常: {str(e)}")
            return None

        return None

    def _fallback_parse_query(self, query: str) -> Dict[str, Any]:
        """备用关键词匹配解析"""
        equipment_info = {
            'equipment_id': None,
            'component': None,
            'symptom': None,
            'symptoms': [],
            'sensor_type': None,
            'llm_parsed': False
        }

        query_lower = query.lower()

        # 识别设备ID
        import re
        for i in range(1, 10):
            if f"{i}号风机" in query or f"fan_{i}" in query_lower:
                equipment_info['equipment_id'] = f"fan_{i}"
                break

        # 识别组件
        if any(keyword in query_lower for keyword in ['轴承', 'bearing']):
            equipment_info['component'] = 'bearing'
        elif any(keyword in query_lower for keyword in ['传动', '驱动', 'drive']):
            equipment_info['component'] = 'drive'
        elif any(keyword in query_lower for keyword in ['电机', 'motor']):
            equipment_info['component'] = 'motor'

        # 识别症状
        symptoms = []
        if any(keyword in query for keyword in ['异响', '噪声', '声音']):
            symptoms.append('异响')
            equipment_info['symptom'] = '异响'
        if any(keyword in query for keyword in ['温度', '发热', '过热']):
            symptoms.append('温度异常')
            equipment_info['symptom'] = '温度异常'
        if any(keyword in query for keyword in ['振动', '抖动']):
            symptoms.append('振动异常')
            equipment_info['symptom'] = '振动异常'

        equipment_info['symptoms'] = symptoms

        print(f"🔧 备用解析结果: {equipment_info}")
        return equipment_info

    def _extract_tool_specifications(self, query: str) -> str:
        """使用大模型智能提取工具指定"""
        try:
            # 如果在解析查询时已经获得了诊断方法，直接使用
            if hasattr(self, '_last_parse_result') and self._last_parse_result:
                diagnosis_method = self._last_parse_result.get('diagnosis_method')
                if diagnosis_method:
                    return self._convert_diagnosis_method_to_specification(diagnosis_method)

            # 使用大模型解析工具指定
            llm_result = self._llm_extract_tool_specifications(query)
            if llm_result:
                return llm_result
        except Exception as e:
            print(f"--大模型工具解析失败，使用备用方法: {str(e)}")

        # 备用：关键词匹配
        return self._fallback_extract_tool_specifications(query)

    def _llm_extract_tool_specifications(self, query: str) -> str:
        """使用大模型提取工具指定"""
        from utils.llm_utils import call_deepseek_api

        system_prompt = """你是一个设备诊断工具识别专家。请分析用户查询，识别用户指定的诊断方法。

支持的诊断方法：
1. CNN诊断/深度学习诊断 - 关键词：CNN、深度学习、神经网络、AI诊断、机器学习
2. 传统诊断 - 关键词：传统诊断、规则诊断、传统方法、经验诊断
3. 模式匹配诊断 - 关键词：模式匹配、故障模式、模式识别
4. 混合诊断 - 关键词：混合诊断、综合诊断、多方法诊断

请返回用户指定的诊断方法，格式为："请使用XXX诊断"。如果用户没有明确指定，返回空字符串。

示例：
- "请使用CNN诊断风机故障" → "请使用CNN诊断"
- "用传统方法分析设备问题" → "请用传统诊断方法"
- "分析设备故障原因" → ""（未指定）"""

        user_prompt = f"请分析以下查询中的诊断方法指定：\n\n{query}"

        try:
            response = call_deepseek_api(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1
            )

            # 清理响应
            result = response.strip().strip('"').strip("'")
            print(f"》》大模型工具解析: '{result}'")
            return result

        except Exception as e:
            print(f"--大模型工具解析异常: {str(e)}")
            return ""

    def _convert_diagnosis_method_to_specification(self, diagnosis_method: str) -> str:
        """将诊断方法转换为工具指定格式"""
        if not diagnosis_method:
            return ""

        method_lower = diagnosis_method.lower()

        if any(keyword in method_lower for keyword in ['cnn', '深度学习', '神经网络', 'ai']):
            return "请使用CNN诊断"
        elif any(keyword in method_lower for keyword in ['传统', '规则', '经验']):
            return "请用传统诊断方法"
        elif any(keyword in method_lower for keyword in ['模式匹配', '模式识别']):
            return "请用模式匹配方法"
        elif any(keyword in method_lower for keyword in ['混合', '综合', '多方法']):
            return "请用混合诊断方法"

        return ""

    def _fallback_extract_tool_specifications(self, query: str) -> str:
        """备用关键词匹配提取工具指定"""
        query_lower = query.lower()

        # 检查各种工具指定
        tool_specs = []

        if any(keyword in query_lower for keyword in ['cnn', '卷积神经网络', '深度学习', '神经网络', 'ai诊断']):
            tool_specs.append('请使用CNN诊断')

        if any(keyword in query_lower for keyword in ['传统诊断', '规则诊断', '传统方法', '传统故障诊断', '传统诊断方法']):
            tool_specs.append('请用传统诊断方法')

        if any(keyword in query_lower for keyword in ['模式匹配', '故障模式', '模式识别', '模式匹配方法']):
            tool_specs.append('请用模式匹配方法')

        if any(keyword in query_lower for keyword in ['混合诊断', '综合诊断', '多方法', '融合诊断']):
            tool_specs.append('请用混合诊断方法')

        result = '，'.join(tool_specs) if tool_specs else ''
        print(f"--备用工具解析: '{result}'")
        return result

    def _coordinate_agents(self, equipment_info: Dict[str, Any], original_query: str) -> Dict[str, Any]:
        """协调多个Agent处理"""
        coordination_result = {
            'data_result': None,
            'diagnosis_result': None,
            'knowledge_result': None,
            'agent_interactions': []
        }
        
        try:
            # 阶段1: 数据收集
            print("阶段1: 数据收集...")
            data_result = self._execute_data_phase(equipment_info, original_query)
            coordination_result['data_result'] = data_result
            coordination_result['agent_interactions'].append({
                'phase': 'data_collection',
                'agent': 'DeepSeekDataAgent',
                'success': data_result.get('success', False)
            })
            
            # 阶段2: 故障诊断
            print("阶段2: 故障诊断...")
            diagnosis_result = self._execute_diagnosis_phase(equipment_info, original_query, data_result)
            coordination_result['diagnosis_result'] = diagnosis_result
            coordination_result['agent_interactions'].append({
                'phase': 'fault_diagnosis',
                'agent': 'DeepSeekDiagnosisAgent',
                'success': diagnosis_result.get('success', False)
            })
            
            # 阶段3: 知识推荐
            print("阶段3: 知识推荐...")
            knowledge_result = self._execute_knowledge_phase(equipment_info, original_query, diagnosis_result)
            coordination_result['knowledge_result'] = knowledge_result
            coordination_result['agent_interactions'].append({
                'phase': 'knowledge_recommendation',
                'agent': 'DeepSeekKnowledgeAgent',
                'success': knowledge_result.get('success', False)
            })
            
        except Exception as e:
            print(f"Agent协调错误: {str(e)}")
            coordination_result['coordination_error'] = str(e)
        
        return coordination_result
    
    def _execute_data_phase(self, equipment_info: Dict[str, Any], query: str) -> Dict[str, Any]:
        """执行数据收集阶段"""
        try:
            equipment_id = equipment_info['equipment_id']
            component = equipment_info.get('component')
            
            # 构建数据查询
            data_query = f"获取设备 {equipment_id} 的数据"
            if component:
                data_query += f"，重点关注 {component} 组件"
            
            # 调用数据Agent
            data_result = self.data_agent.process(data_query, equipment_info)
            
            return data_result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_diagnosis_phase(self, equipment_info: Dict[str, Any], query: str, data_result: Dict[str, Any]) -> Dict[str, Any]:
        """执行故障诊断阶段"""
        try:
            equipment_id = equipment_info['equipment_id']
            component = equipment_info.get('component', 'unknown')
            symptoms = equipment_info.get('symptoms', [])

            # 构建诊断查询，保留用户的原始工具指定
            diagnosis_query = f"对设备 {equipment_id} 的 {component} 组件进行故障诊断"
            if symptoms:
                diagnosis_query += f"，观察到的症状包括：{', '.join(symptoms)}"

            # 重要：保留用户的工具指定
            tool_specifications = self._extract_tool_specifications(query)
            if tool_specifications:
                diagnosis_query += f"。{tool_specifications}"

            # 添加数据结果作为上下文
            context = {
                'equipment_info': equipment_info,
                'data_result': data_result
            }

            # 调用诊断Agent
            diagnosis_result = self.diagnosis_agent.process(diagnosis_query, context)

            return diagnosis_result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_knowledge_phase(self, equipment_info: Dict[str, Any], query: str, diagnosis_result: Dict[str, Any]) -> Dict[str, Any]:
        """执行知识推荐阶段"""
        try:
            component = equipment_info.get('component', 'unknown')
            symptom = equipment_info.get('symptom', '')
            
            # 构建知识查询
            knowledge_query = f"搜索关于 {component} 组件 {symptom} 问题的维护知识和建议"
            
            # 添加诊断结果作为上下文
            context = {
                'equipment_info': equipment_info,
                'diagnosis_result': diagnosis_result
            }
            
            # 调用知识Agent
            knowledge_result = self.knowledge_agent.process(knowledge_query, context)
            
            return knowledge_result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _integrate_results(self, coordination_result: Dict[str, Any], equipment_info: Dict[str, Any]) -> Dict[str, Any]:
        """整合各Agent的结果"""
        integrated_result = {
            'equipment_info': equipment_info,
            'coordination_summary': {
                'total_agents': 3,
                'successful_agents': 0,
                'failed_agents': 0
            },
            'data_analysis': {},
            'diagnosis': {},
            'knowledge_recommendations': {},
            'overall_status': 'unknown',
            'mode': 'deepseek'
        }
        
        # 整合数据分析结果
        data_result = coordination_result.get('data_result')
        if data_result and data_result.get('success'):
            integrated_result['coordination_summary']['successful_agents'] += 1
            output = data_result.get('output', '') or data_result.get('agent_output', '')
            # 确保输出不为空且不是错误信息
            if output and len(str(output).strip()) > 10 and 'API调用失败' not in str(output):
                integrated_result['data_analysis'] = {
                    'agent_output': output,
                    'tool_results': data_result.get('tool_results', {})
                }
            else:
                integrated_result['coordination_summary']['failed_agents'] += 1
                integrated_result['data_analysis'] = {
                    'agent_output': '数据分析Agent响应异常，请检查API配置',
                    'tool_results': {}
                }
        else:
            integrated_result['coordination_summary']['failed_agents'] += 1
            integrated_result['data_analysis'] = {
                'agent_output': '数据分析Agent处理失败',
                'tool_results': {}
            }
        
        # 整合诊断结果
        diagnosis_result = coordination_result.get('diagnosis_result')
        if diagnosis_result and diagnosis_result.get('success'):
            output = diagnosis_result.get('output', '') or diagnosis_result.get('agent_output', '')
            # 确保输出不为空且不是错误信息
            if output and len(str(output).strip()) > 10 and 'API调用失败' not in str(output):
                integrated_result['coordination_summary']['successful_agents'] += 1
                integrated_result['diagnosis'] = {
                    'agent_output': output,
                    'tool_results': diagnosis_result.get('tool_results', {})
                }
            else:
                integrated_result['coordination_summary']['failed_agents'] += 1
                integrated_result['diagnosis'] = {
                    'agent_output': '故障诊断Agent响应异常，请检查API配置',
                    'tool_results': {}
                }
        else:
            integrated_result['coordination_summary']['failed_agents'] += 1
            integrated_result['diagnosis'] = {
                'agent_output': '故障诊断Agent处理失败',
                'tool_results': {}
            }
        
        # 整合知识推荐结果
        knowledge_result = coordination_result.get('knowledge_result')
        if knowledge_result and knowledge_result.get('success'):
            output = knowledge_result.get('output', '') or knowledge_result.get('agent_output', '')
            # 确保输出不为空且不是错误信息
            if output and len(str(output).strip()) > 10 and 'API调用失败' not in str(output):
                integrated_result['coordination_summary']['successful_agents'] += 1
                integrated_result['knowledge_recommendations'] = {
                    'agent_output': output,
                    'tool_results': knowledge_result.get('tool_results', {})
                }
            else:
                integrated_result['coordination_summary']['failed_agents'] += 1
                integrated_result['knowledge_recommendations'] = {
                    'agent_output': '知识推荐Agent响应异常，请检查API配置',
                    'tool_results': {}
                }
        else:
            integrated_result['coordination_summary']['failed_agents'] += 1
            integrated_result['knowledge_recommendations'] = {
                'agent_output': '知识推荐Agent处理失败',
                'tool_results': {}
            }
        
        # 确定总体状态
        successful_agents = integrated_result['coordination_summary']['successful_agents']
        if successful_agents >= 2:
            integrated_result['overall_status'] = 'success'
        elif successful_agents >= 1:
            integrated_result['overall_status'] = 'partial_success'
        else:
            integrated_result['overall_status'] = 'failed'
            # 如果所有Agent都失败，可能是API问题，使用简化协调器
            integrated_result['fallback_suggestion'] = '建议切换到简化协调器或检查DeepSeek API配置'
        
        # 添加Agent交互信息
        integrated_result['agent_interactions'] = coordination_result.get('agent_interactions', [])
        
        return integrated_result
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            'error': error_message,
            'success': False,
            'mode': 'deepseek',
            'coordination_summary': {
                'total_agents': 0,
                'successful_agents': 0,
                'failed_agents': 0
            }
        }
    
    def _record_coordination(self, query: str, result: Dict[str, Any]):
        """记录协调历史"""
        record = {
            'timestamp': time.time(),
            'query': query,
            'success': result.get('overall_status') == 'success',
            'agents_used': result.get('coordination_summary', {}).get('successful_agents', 0),
            'mode': result.get('mode', 'deepseek')
        }
        
        self.coordination_history.append(record)
        
        # 限制历史记录长度
        if len(self.coordination_history) > 100:
            self.coordination_history = self.coordination_history[-100:]
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """获取协调统计信息"""
        if not self.coordination_history:
            return {'total_queries': 0, 'success_rate': 0.0}
        
        total_queries = len(self.coordination_history)
        successful_queries = sum(1 for record in self.coordination_history if record['success'])
        success_rate = successful_queries / total_queries
        
        return {
            'total_queries': total_queries,
            'successful_queries': successful_queries,
            'success_rate': success_rate,
            'average_agents_per_query': sum(record['agents_used'] for record in self.coordination_history) / total_queries,
            'deepseek_queries': sum(1 for record in self.coordination_history if record['mode'] == 'deepseek'),
            'fallback_queries': sum(1 for record in self.coordination_history if record['mode'] == 'fallback')
        }

    def _generate_user_friendly_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """生成用户友好的响应格式"""
        try:
            # 保留原始结构，但确保有用户友好的输出
            user_result = result.copy()

            # 为每个Agent添加用户友好的输出
            for agent_key in ['data_analysis', 'diagnosis', 'knowledge_recommendations']:
                if agent_key in result and isinstance(result[agent_key], dict):
                    agent_data = result[agent_key]

                    # 如果没有agent_output，尝试从output中获取
                    if 'agent_output' not in agent_data and 'output' in agent_data:
                        agent_data['agent_output'] = agent_data['output']

                    # 如果仍然没有有效输出，生成默认消息
                    if not agent_data.get('agent_output') or len(str(agent_data.get('agent_output')).strip()) < 10:
                        if agent_key == 'data_analysis':
                            agent_data['agent_output'] = "数据分析Agent正在处理中，请稍后查看详细分析结果。"
                        elif agent_key == 'diagnosis':
                            agent_data['agent_output'] = "故障诊断Agent正在分析中，请稍后查看诊断结果。"
                        elif agent_key == 'knowledge_recommendations':
                            agent_data['agent_output'] = "知识推荐Agent正在搜索相关维护知识。"

            return user_result

        except Exception as e:
            print(f"生成用户友好响应失败: {str(e)}")
            return result

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            'error': error_message,
            'success': False,
            'mode': 'deepseek'
        }

    def _generate_user_friendly_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """生成用户友好的响应"""
        # 确保每个部分都有有效的输出
        for key in ['data_analysis', 'diagnosis', 'knowledge_recommendations']:
            if key in result and isinstance(result[key], dict):
                if not result[key].get('agent_output'):
                    result[key]['agent_output'] = f"{key.replace('_', ' ').title()}暂时不可用"

        return result

    def _record_coordination(self, query: str, result: Dict[str, Any]) -> None:
        """记录协调历史"""
        # 这里可以添加日志记录逻辑
        pass
