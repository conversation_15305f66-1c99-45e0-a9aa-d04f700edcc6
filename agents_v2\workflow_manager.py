"""
工作流管理器 - 根据用户输入动态生成和执行工作流
"""
from typing import Dict, Any, List, Tuple, Optional
import re

class WorkflowManager:
    """动态工作流管理器"""
    
    def __init__(self):
        self.workflow_patterns = self._initialize_workflow_patterns()
        self.agent_registry = {}
    
    def register_agent(self, agent_type: str, agent_class):
        """注册Agent类"""
        self.agent_registry[agent_type] = agent_class
        print(f"--注册Agent: {agent_type}")
    
    def _initialize_workflow_patterns(self) -> Dict[str, Dict]:
        """初始化工作流模式"""
        return {
            "fault_diagnosis": {
                "keywords": [
                    "故障", "诊断", "异常", "问题", "故障诊断", "异响", "振动", "温度过高", 
                    "噪声", "不正常", "检查", "分析", "原因", "维修", "修理"
                ],
                "agents": ["data", "diagnosis", "knowledge"],
                "description": "故障诊断工作流",
                "priority": 1
            },
            "health_assessment": {
                "keywords": [
                    "健康", "状态", "评估", "健康评估", "设备状态", "健康状况", 
                    "设备健康", "状态分析", "健康分析", "健康检查", "状态检测"
                ],
                "agents": ["data", "health"],
                "description": "健康评估工作流",
                "priority": 2
            },
            "lifespan_prediction": {
                "keywords": [
                    "寿命", "预测", "剩余", "使用寿命", "寿命预测", "剩余寿命", 
                    "预期寿命", "寿命分析", "寿命评估", "更换", "替换", "维护计划",
                    "退化", "老化", "失效", "报废"
                ],
                "agents": ["data", "lifespan"],
                "description": "寿命预测工作流",
                "priority": 3
            },
            "comprehensive_analysis": {
                "keywords": [
                    "全面", "综合", "完整", "详细", "全面分析", "综合评估", 
                    "完整检查", "详细分析", "整体", "全方位"
                ],
                "agents": ["data", "diagnosis", "health", "lifespan", "knowledge"],
                "description": "综合分析工作流",
                "priority": 4
            },
            "data_analysis": {
                "keywords": [
                    "数据", "数据分析", "传感器", "监测", "数据查看", "数据检查",
                    "参数", "指标", "测量", "记录"
                ],
                "agents": ["data"],
                "description": "数据分析工作流",
                "priority": 5
            },
            "knowledge_search": {
                "keywords": [
                    "知识", "搜索", "查找", "资料", "文档", "手册", "指南",
                    "经验", "建议", "方法", "步骤", "操作"
                ],
                "agents": ["knowledge"],
                "description": "知识搜索工作流",
                "priority": 6
            }
        }
    
    def analyze_user_intent(self, user_input: str) -> Tuple[str, float]:
        """分析用户意图，返回最匹配的工作流类型和置信度"""
        user_input_lower = user_input.lower()
        
        workflow_scores = {}
        
        for workflow_type, pattern in self.workflow_patterns.items():
            score = 0
            keyword_matches = 0
            
            # 计算关键词匹配分数
            for keyword in pattern["keywords"]:
                if keyword in user_input_lower:
                    keyword_matches += 1
                    # 根据关键词长度给予不同权重
                    score += len(keyword) * 2
            
            # 考虑优先级（优先级越低，分数加成越高）
            priority_bonus = (10 - pattern["priority"]) * 5
            score += priority_bonus
            
            # 如果有关键词匹配，给予基础分数
            if keyword_matches > 0:
                score += keyword_matches * 10
                workflow_scores[workflow_type] = score
        
        if not workflow_scores:
            # 如果没有匹配，默认使用故障诊断工作流
            return "fault_diagnosis", 0.5
        
        # 找到得分最高的工作流
        best_workflow = max(workflow_scores.items(), key=lambda x: x[1])
        workflow_type = best_workflow[0]
        
        # 计算置信度（0-1之间）
        max_score = best_workflow[1]
        confidence = min(1.0, max_score / 100.0)
        
        return workflow_type, confidence
    
    def generate_workflow(self, workflow_type: str, user_input: str) -> Dict[str, Any]:
        """生成具体的工作流执行计划"""
        if workflow_type not in self.workflow_patterns:
            workflow_type = "fault_diagnosis"  # 默认工作流
        
        pattern = self.workflow_patterns[workflow_type]
        agents = pattern["agents"]
        
        # 生成工作流步骤
        workflow_steps = []
        
        for i, agent_type in enumerate(agents):
            step = {
                "step_id": i + 1,
                "agent_type": agent_type,
                "agent_name": f"DeepSeek{agent_type.title()}Agent",
                "description": self._get_step_description(agent_type, workflow_type),
                "depends_on": list(range(1, i + 1)) if i > 0 else [],
                "context_sharing": True
            }
            workflow_steps.append(step)
        
        workflow = {
            "workflow_id": f"{workflow_type}_{hash(user_input) % 10000}",
            "workflow_type": workflow_type,
            "description": pattern["description"],
            "user_input": user_input,
            "steps": workflow_steps,
            "total_steps": len(workflow_steps),
            "context": self._extract_workflow_context(user_input, workflow_type)
        }
        
        return workflow
    
    def _get_step_description(self, agent_type: str, workflow_type: str) -> str:
        """获取工作流步骤描述"""
        descriptions = {
            "data": "数据收集和分析",
            "diagnosis": "故障诊断分析", 
            "health": "健康状态评估",
            "lifespan": "寿命预测分析",
            "knowledge": "知识推荐和建议"
        }
        
        base_desc = descriptions.get(agent_type, f"{agent_type}分析")
        
        # 根据工作流类型调整描述
        if workflow_type == "comprehensive_analysis":
            return f"综合{base_desc}"
        elif workflow_type == "health_assessment" and agent_type == "data":
            return "健康评估数据收集"
        elif workflow_type == "lifespan_prediction" and agent_type == "data":
            return "寿命预测数据收集"
        
        return base_desc
    
    def _extract_workflow_context(self, user_input: str, workflow_type: str) -> Dict[str, Any]:
        """提取工作流上下文信息"""
        context = {
            "workflow_type": workflow_type,
            "user_input": user_input
        }
        
        # 提取设备信息
        equipment_match = re.search(r'(\d+)号?风机', user_input)
        if equipment_match:
            context["equipment_id"] = f"fan_{equipment_match.group(1)}"
        
        # 提取组件信息
        component_keywords = {
            "轴承": "bearing",
            "驱动": "drive", 
            "传动": "drive",
            "电机": "motor",
            "齿轮箱": "gearbox",
            "齿轮": "gearbox"
        }
        
        for keyword, component in component_keywords.items():
            if keyword in user_input:
                context["component"] = component
                break
        
        # 提取分析重点
        if "温度" in user_input:
            context["focus_temperature"] = True
        if "振动" in user_input:
            context["focus_vibration"] = True
        if "噪声" in user_input or "噪音" in user_input:
            context["focus_noise"] = True
        
        # 提取紧急程度
        if any(word in user_input for word in ["紧急", "立即", "马上", "急"]):
            context["urgency"] = "high"
        elif any(word in user_input for word in ["尽快", "及时"]):
            context["urgency"] = "medium"
        else:
            context["urgency"] = "normal"
        
        return context
    
    def execute_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """执行工作流"""
        print(f"--开始执行工作流: {workflow['description']}")
        print(f"--工作流ID: {workflow['workflow_id']}")
        print(f"--总步骤数: {workflow['total_steps']}")
        
        results = {}
        shared_context = workflow.get("context", {})
        
        for step in workflow["steps"]:
            step_id = step["step_id"]
            agent_type = step["agent_type"]
            agent_name = step["agent_name"]
            
            print(f"--执行步骤 {step_id}: {step['description']}")
            
            try:
                # 获取Agent实例
                if agent_type not in self.agent_registry:
                    print(f"--警告: Agent类型 {agent_type} 未注册，跳过此步骤")
                    continue
                
                agent_class = self.agent_registry[agent_type]
                agent = agent_class()
                
                # 准备输入数据
                step_input = workflow["user_input"]
                
                # 传递共享上下文
                step_context = shared_context.copy()
                
                # 添加前面步骤的结果到上下文
                if step["context_sharing"] and results:
                    step_context["tool_results"] = {}
                    for prev_result in results.values():
                        if isinstance(prev_result, dict) and "tool_results" in prev_result:
                            step_context["tool_results"].update(prev_result["tool_results"])
                        
                        # 传递传感器数据
                        if isinstance(prev_result, dict) and "tool_results" in prev_result:
                            for tool_result in prev_result["tool_results"].values():
                                if isinstance(tool_result, dict) and "sensor_data" in tool_result:
                                    step_context["sensor_data"] = tool_result["sensor_data"]
                                    break
                        
                        # 传递健康评分
                        if isinstance(prev_result, dict) and "health_summary" in prev_result:
                            health_summary = prev_result["health_summary"]
                            if "health_score" in health_summary:
                                step_context["health_score"] = health_summary["health_score"]
                
                # 执行Agent
                step_result = agent.process(step_input, step_context)
                results[f"step_{step_id}_{agent_type}"] = step_result
                
                print(f"--步骤 {step_id} 完成: {step_result.get('success', False)}")
                
            except Exception as e:
                print(f"--步骤 {step_id} 执行失败: {str(e)}")
                results[f"step_{step_id}_{agent_type}"] = {
                    "success": False,
                    "error": str(e),
                    "agent_output": f"步骤执行失败: {str(e)}"
                }
        
        # 整合工作流结果
        workflow_result = self._integrate_workflow_results(workflow, results)
        
        print(f"--工作流执行完成: {workflow['workflow_id']}")
        return workflow_result
    
    def _integrate_workflow_results(self, workflow: Dict[str, Any], step_results: Dict[str, Any]) -> Dict[str, Any]:
        """整合工作流执行结果"""
        successful_steps = 0
        total_steps = workflow["total_steps"]
        
        # 收集所有成功的结果
        integrated_results = {}
        all_tool_results = {}
        
        for step_key, result in step_results.items():
            if isinstance(result, dict):
                if result.get("success", False):
                    successful_steps += 1
                
                # 收集工具结果
                if "tool_results" in result:
                    all_tool_results.update(result["tool_results"])
                
                # 收集特定类型的结果
                if "health_summary" in result:
                    integrated_results["health_assessment"] = result
                elif "lifespan_summary" in result:
                    integrated_results["lifespan_prediction"] = result
                elif "diagnosis" in step_key:
                    integrated_results["fault_diagnosis"] = result
                elif "data" in step_key:
                    integrated_results["data_analysis"] = result
                elif "knowledge" in step_key:
                    integrated_results["knowledge_recommendations"] = result
        
        # 生成工作流摘要
        workflow_summary = {
            "workflow_id": workflow["workflow_id"],
            "workflow_type": workflow["workflow_type"],
            "description": workflow["description"],
            "total_steps": total_steps,
            "successful_steps": successful_steps,
            "success_rate": successful_steps / total_steps if total_steps > 0 else 0,
            "execution_status": "completed" if successful_steps == total_steps else "partial"
        }
        
        return {
            "workflow_summary": workflow_summary,
            "step_results": step_results,
            "integrated_results": integrated_results,
            "all_tool_results": all_tool_results,
            "success": successful_steps > 0
        }
