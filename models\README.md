# 深度学习模型目录

本目录存放用于设备健康评估和寿命预测的深度学习模型。

## 模型文件

### CAE健康评估模型
- **文件名**: `cae_health_assessment.h5`
- **模型类型**: 卷积自编码器 (Convolutional AutoEncoder)
- **用途**: 设备健康状态评估
- **输入**: (50, 6) - 50个时间步，6个特征
- **输出**: 重构数据，用于计算重构误差

### RNN寿命预测模型
- **文件名**: `rnn_lifespan_prediction.h5`
- **模型类型**: 循环神经网络 (Recurrent Neural Network)
- **用途**: 设备剩余使用寿命预测
- **输入**: (30, 6) - 30个时间步，6个特征
- **输出**: 剩余寿命天数

## 特征说明

模型使用的6个特征：
1. **temperature** - 温度 (°C)
2. **vibration** - 振动 (mm/s)
3. **noise** - 噪声 (dB)
4. **speed** - 转速 (rpm)
5. **current** - 电流 (A)
6. **power** - 功率 (kW)

## 模型训练

如果需要重新训练模型，请参考以下步骤：

1. 准备训练数据
2. 运行训练脚本
3. 保存训练好的模型到此目录

## 注意事项

- 模型文件较大，不包含在版本控制中
- 如果模型文件不存在，系统会自动创建新模型或使用模拟模式
- 建议定期使用新数据重新训练模型以提高准确性
