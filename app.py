
from flask import Flask, request, jsonify, render_template
import os
import sys
import traceback
import json

app = Flask(__name__)

print("="*60)
print("多Agent故障诊断系统启动中...")
print("="*60)

# 初始化协调器
coordinators = {}

# 1. 加载简化协调器
print("--加载简化协调器...")
try:
    from agents_v2.simple_coordinator import SimpleMultiAgentCoordinator
    coordinators['simple'] = SimpleMultiAgentCoordinator()
    print("--简化协调器加载成功")
except Exception as e:
    print(f"--简化协调器加载失败: {str(e)}")

# 2. 加载DeepSeek协调器
print("--加载DeepSeek协调器...")
try:
    from agents_v2.deepseek_coordinator import DeepSeekMultiAgentCoordinator
    coordinators['deepseek'] = DeepSeekMultiAgentCoordinator()
    print("--DeepSeek协调器加载成功")
except Exception as e:
    print(f"--DeepSeek协调器加载失败: {str(e)}")
    traceback.print_exc()

print(f"-可用协调器: {list(coordinators.keys())}")

# 检查DeepSeek API配置
deepseek_configured = bool(os.getenv('DEEPSEEK_API_KEY') and 
                          os.getenv('DEEPSEEK_API_KEY') != 'your_deepseek_api_key_here')
print(f"--DeepSeek API: {'已配置' if deepseek_configured else '未配置'}")
print("="*60)

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/api/coordinators')
def get_coordinators():
    """获取可用协调器"""
    available = list(coordinators.keys())
    default = 'deepseek' if 'deepseek' in available else 'simple' if 'simple' in available else None
    
    return jsonify({
        'available_coordinators': available,
        'default': default,
        'deepseek_configured': deepseek_configured
    })

@app.route('/api/test')
def test_api():
    """测试API"""
    return jsonify({
        'status': 'ok',
        'message': '多Agent故障诊断系统运行正常',
        'coordinators': list(coordinators.keys()),
        'deepseek_api': 'configured' if deepseek_configured else 'not_configured',
        'version': 'fixed_v1.0'
    })

@app.route('/api/diagnose', methods=['POST'])
def diagnose():
    """故障诊断"""
    try:
        data = request.json
        query = data.get('query', '').strip()
        coordinator_type = data.get('coordinator', 'auto')
        
        print(f"\n --收到诊断请求:")
        print(f"   查询: {query}")
        print(f"   协调器: {coordinator_type}")
        
        if not query:
            return jsonify({'error': '请输入查询内容'}), 400
        
        # 选择协调器
        if coordinator_type == 'auto':
            if 'deepseek' in coordinators:
                coordinator_type = 'deepseek'
            elif 'simple' in coordinators:
                coordinator_type = 'simple'
            else:
                return jsonify({'error': '没有可用的协调器'}), 500
        
        if coordinator_type not in coordinators:
            return jsonify({
                'error': f'协调器 {coordinator_type} 不可用',
                'available': list(coordinators.keys())
            }), 400
        
        # 处理查询
        coordinator = coordinators[coordinator_type]
        print(f"--使用协调器: {coordinator_type}")
        
        try:
            result = coordinator.process_query(query)
            
            # 确保结果包含必要的字段
            if not isinstance(result, dict):
                result = {'output': str(result), 'success': False}
            
            # 添加元信息
            result['coordinator_used'] = coordinator_type
            result['query'] = query
            result['success'] = result.get('success', True)
            
            # 调试信息
            print(f"--处理结果类型: {type(result)}")
            print(f"--结果键: {list(result.keys()) if isinstance(result, dict) else 'not dict'}")
            
            # 特殊处理DeepSeek协调器的结果
            if coordinator_type == 'deepseek':
                print(f"--DeepSeek结果处理...")
                
                # 检查是否有实际的Agent输出
                has_content = False
                analysis_keys = ['data_analysis', 'diagnosis_analysis', 'health_analysis',
                               'lifespan_analysis', 'knowledge_analysis', 'diagnosis', 'knowledge_recommendations']

                for key in analysis_keys:
                    if key in result and isinstance(result[key], dict):
                        agent_data = result[key]
                        if 'agent_output' in agent_data or 'output' in agent_data:
                            output = agent_data.get('agent_output') or agent_data.get('output')
                            if output and len(str(output).strip()) > 10:
                                has_content = True
                                print(f"--找到 {key} 的有效输出")
                        # 也检查工具结果
                        elif 'tool_results' in agent_data:
                            tool_results = agent_data['tool_results']
                            if isinstance(tool_results, dict) and len(tool_results) > 0:
                                has_content = True
                                print(f"--找到 {key} 的工具结果")
                
                if not has_content:
                    print(f"--DeepSeek协调器没有返回有效内容，添加调试信息")
                    result['debug_info'] = {
                        'message': 'DeepSeek协调器响应异常',
                        'raw_keys': list(result.keys()),
                        'suggestion': '请检查DeepSeek API配置或尝试简化协调器'
                    }
            
            print(f"--处理完成")
            return jsonify(result)
            
        except Exception as coord_error:
            print(f"--协调器错误: {str(coord_error)}")
            traceback.print_exc()
            
            # 失败时回退到简化版本
            if coordinator_type != 'simple' and 'simple' in coordinators:
                print("--回退到简化协调器...")
                try:
                    result = coordinators['simple'].process_query(query)
                    result['coordinator_used'] = 'simple'
                    result['query'] = query
                    result['note'] = f'原协调器({coordinator_type})失败，已回退'
                    result['original_error'] = str(coord_error)
                    result['success'] = True
                    return jsonify(result)
                except Exception as fallback_error:
                    print(f"--回退失败: {str(fallback_error)}")
            
            return jsonify({
                'error': f'处理失败: {str(coord_error)}',
                'coordinator_type': coordinator_type,
                'success': False,
                'traceback': traceback.format_exc()[-500:]  # 限制错误信息长度
            }), 500
    
    except Exception as e:
        print(f"--API错误: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'error': f'API处理失败: {str(e)}',
            'success': False
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': '页面未找到'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("\n" + "="*60)
    print("系统启动完成!")
    print("="*60)
    print(f"--可用协调器: {list(coordinators.keys())}")
    print(f"--DeepSeek API: {'已配置' if deepseek_configured else '未配置'}")
    print(f"--数据目录: data/ (包含本地传感器数据)")
    print("="*60)
    print("--访问地址: http://127.0.0.1:5000")
    print("="*60)
    
    try:
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n--系统已停止")
    except Exception as e:
        print(f"--启动失败: {str(e)}")
        print("--请检查端口5000是否被占用")
