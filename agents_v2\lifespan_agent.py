"""
寿命预测Agent - 基于RNN模型预测设备剩余使用寿命
"""
from typing import Dict, Any, List
from .base_deepseek_agent import DeepSeekAgent
from tools.lifespan_prediction_tools import LifespanPredictionTool

class DeepSeekLifespanAgent(DeepSeekAgent):
    """设备寿命预测Agent"""
    
    def __init__(self):
        # 初始化寿命预测工具
        tools = [LifespanPredictionTool()]
        
        super().__init__(
            name="DeepSeekLifespanAgent",
            description="专门负责设备剩余使用寿命预测的智能Agent，使用RNN模型分析设备寿命",
            tools=tools
        )
        print("--DeepSeekLifespanAgent 寿命预测工具初始化成功")
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的设备寿命预测专家。你的主要职责是：

1. 分析设备历史数据和当前状态，预测剩余使用寿命
2. 使用RNN(循环神经网络)模型进行时序数据分析
3. 评估设备退化趋势和风险因素
4. 提供维护计划和更换建议

分析要点：
- 重点关注设备的退化趋势和模式
- 分析时序数据中的长期依赖关系
- 评估各种风险因素对寿命的影响
- 提供具体的时间预测和维护建议

请用专业、准确的中文回答，重点突出寿命预测结果和维护计划。"""
    
    def _should_use_tool(self, tool, input_data: str) -> bool:
        """判断是否应该使用特定工具"""
        tool_name = tool.__class__.__name__
        
        # 寿命预测相关关键词
        lifespan_keywords = [
            "寿命", "预测", "剩余", "使用寿命", "寿命预测", 
            "剩余寿命", "预期寿命", "寿命分析", "寿命评估",
            "更换", "替换", "维护计划", "退化", "老化"
        ]
        
        if "LifespanPredictionTool" in tool_name:
            return any(keyword in input_data for keyword in lifespan_keywords)
        
        return False
    
    def _extract_lifespan_context(self, input_data: str) -> Dict[str, Any]:
        """提取寿命预测相关的上下文信息"""
        context = {}
        
        # 提取预测类型
        if "详细" in input_data or "全面" in input_data:
            context["prediction_type"] = "comprehensive"
        elif "快速" in input_data or "简单" in input_data:
            context["prediction_type"] = "quick"
        else:
            context["prediction_type"] = "standard"
        
        # 提取时间范围
        if "短期" in input_data:
            context["time_horizon"] = "short"
        elif "长期" in input_data:
            context["time_horizon"] = "long"
        else:
            context["time_horizon"] = "medium"
        
        # 提取关注因素
        focus_factors = []
        if "温度" in input_data:
            focus_factors.append("temperature")
        if "振动" in input_data:
            focus_factors.append("vibration")
        if "磨损" in input_data:
            focus_factors.append("wear")
        if "疲劳" in input_data:
            focus_factors.append("fatigue")
        
        if focus_factors:
            context["focus_factors"] = focus_factors
        
        return context
    
    def _call_tool(self, tool, input_data: str, context: Dict = None) -> Dict[str, Any]:
        """调用工具的统一接口"""
        tool_name = tool.__class__.__name__
        
        if "LifespanPredictionTool" in tool_name:
            # 寿命预测工具调用
            equipment_id = self._extract_equipment_id(input_data)
            component = self._extract_component(input_data)
            
            # 从上下文获取传感器数据
            sensor_data = None
            health_score = None
            
            if context:
                if 'sensor_data' in context:
                    sensor_data = context['sensor_data']
                
                if 'health_score' in context:
                    health_score = context['health_score']
                
                # 尝试从其他工具结果中获取数据
                if 'tool_results' in context:
                    for tool_result in context['tool_results'].values():
                        if isinstance(tool_result, dict):
                            if 'sensor_data' in tool_result:
                                sensor_data = tool_result['sensor_data']
                            if 'health_score' in tool_result:
                                health_score = tool_result['health_score']
            
            return tool._run(
                equipment_id=equipment_id,
                component=component,
                sensor_data=sensor_data,
                health_score=health_score
            )
        
        # 默认调用
        return tool._run(input_data)
    
    def _generate_comprehensive_response(self, llm_response: str, tool_results: Dict[str, Any], 
                                       input_data: str) -> Dict[str, Any]:
        """生成综合响应"""
        # 提取寿命预测结果
        lifespan_result = None
        for tool_name, result in tool_results.items():
            if "LifespanPredictionTool" in tool_name and isinstance(result, dict) and result.get('success'):
                lifespan_result = result
                break
        
        if lifespan_result:
            # 生成寿命预测报告
            remaining_life = lifespan_result.get('remaining_life', {})
            remaining_days = remaining_life.get('remaining_days', 0)
            remaining_months = remaining_life.get('remaining_months', 0)
            failure_date = remaining_life.get('predicted_failure_date', '未知')
            life_category = remaining_life.get('life_category', '未知')
            urgency = remaining_life.get('urgency', '未知')
            
            # 构建详细的寿命分析
            analysis_parts = [
                f"根据RNN模型分析，设备 {lifespan_result.get('equipment_id', '')} 的 {lifespan_result.get('component', '')} 组件寿命预测结果如下：",
                f"",
                f"⏰ **剩余寿命**: {remaining_days:.1f} 天 ({remaining_months:.1f} 个月)",
                f"📅 **预计失效日期**: {failure_date}",
                f"📊 **寿命类别**: {life_category}",
                f"🚨 **紧急程度**: {urgency}",
                f"🔧 **预测方法**: {lifespan_result.get('prediction_method', 'RNN模型')}",
                f"📈 **置信度**: {lifespan_result.get('confidence', 0):.2%}",
            ]
            
            # 添加置信区间
            confidence_interval = remaining_life.get('confidence_interval', {})
            if confidence_interval:
                lower = confidence_interval.get('lower', 0)
                upper = confidence_interval.get('upper', 0)
                analysis_parts.extend([
                    f"📊 **置信区间**: {lower:.1f} - {upper:.1f} 天",
                ])
            
            # 添加退化趋势分析
            if 'degradation_trends' in lifespan_result:
                trends = lifespan_result['degradation_trends']
                analysis_parts.extend([
                    f"",
                    f"📉 **退化趋势分析**:",
                    f"  - 整体退化趋势: {trends.get('overall_trend', 0):.4f}",
                    f"  - 温度变化趋势: {trends.get('temperature_trend', 0):.4f}",
                    f"  - 振动变化趋势: {trends.get('vibration_trend', 0):.4f}",
                    f"  - 噪声变化趋势: {trends.get('noise_trend', 0):.4f}",
                ])
            
            # 添加风险因素
            if 'risk_factors' in lifespan_result:
                analysis_parts.extend([
                    f"",
                    f"⚠️ **主要风险因素**:",
                ])
                for i, risk in enumerate(lifespan_result['risk_factors'], 1):
                    analysis_parts.append(f"  {i}. {risk}")
            
            # 添加维护建议
            if 'maintenance_recommendations' in lifespan_result:
                analysis_parts.extend([
                    f"",
                    f"🔧 **维护计划建议**:",
                ])
                for i, rec in enumerate(lifespan_result['maintenance_recommendations'], 1):
                    analysis_parts.append(f"  {i}. {rec}")
            
            comprehensive_analysis = "\n".join(analysis_parts)
            
            return {
                "agent_output": comprehensive_analysis,
                "tool_results": tool_results,
                "lifespan_summary": {
                    "remaining_days": remaining_days,
                    "remaining_months": remaining_months,
                    "failure_date": failure_date,
                    "life_category": life_category,
                    "urgency": urgency,
                    "equipment_id": lifespan_result.get('equipment_id'),
                    "component": lifespan_result.get('component')
                },
                "success": True
            }
        else:
            # 没有成功的寿命预测结果
            error_msg = "寿命预测过程中遇到问题，请检查设备数据或稍后重试。"
            
            return {
                "agent_output": error_msg,
                "tool_results": tool_results,
                "success": False,
                "error": "寿命预测工具调用失败"
            }
    
    def process(self, input_data: str, context: Dict = None) -> Dict[str, Any]:
        """处理寿命预测请求"""
        print(f"--DeepSeekLifespanAgent 开始处理寿命预测请求")
        
        try:
            # 提取寿命预测上下文
            lifespan_context = self._extract_lifespan_context(input_data)
            if context:
                context.update(lifespan_context)
            else:
                context = lifespan_context
            
            # 调用父类的处理方法
            result = super().process(input_data, context)
            
            print(f"--DeepSeekLifespanAgent 寿命预测完成")
            return result
            
        except Exception as e:
            print(f"--DeepSeekLifespanAgent 处理失败: {str(e)}")
            return {
                "agent_output": f"寿命预测Agent处理失败: {str(e)}",
                "tool_results": {},
                "success": False,
                "error": str(e)
            }
