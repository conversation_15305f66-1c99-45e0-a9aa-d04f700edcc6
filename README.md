# 多Agent协同设备智能运维系统

基于DeepSeek API和多Agent架构的智能设备故障诊断系统，支持自然语言交互和实时数据分析。

## 🌟 功能特性

### 核心功能
- **🤖 智能对话**: 基于DeepSeek API的中文自然语言交互
- **📊 多Agent协同**: 数据分析、故障诊断、健康评估、寿命预测、知识推荐五大Agent协同工作
- **📈 实时数据分析**: 自动读取本地传感器数据，支持多种数据格式
- **🔧 智能故障诊断**:
  - 传统模式匹配诊断（基于规则和故障模式库）
  - CNN深度学习诊断（基于神经网络的模式识别）
  - 混合诊断方法（结合多种方法的优势）
  - 用户可精确指定诊断方法
- **🏥 设备健康评估**: 基于CAE(卷积自编码器)模型的设备健康状态评估
- **⏰ 寿命预测**: 基于RNN(循环神经网络)模型的设备剩余使用寿命预测
- **📚 知识推荐**: 基于故障类型推荐相关维护知识和操作指南
- **🔄 灵活工作流**: 根据用户输入自动生成和执行相应的工作流
- **📱 现代化界面**: 响应式Web界面，支持实时图表可视化

### 技术特性
- **灵活工作流系统**: 根据用户输入自动识别意图并生成相应工作流
- **双协调器架构**: DeepSeek智能协调器 + 简化本地协调器
- **深度学习模型**: CAE健康评估模型 + RNN寿命预测模型
- **多种诊断工具**: 传统诊断、CNN诊断、模式匹配、混合诊断
- **数据库集成**: MongoDB支持，equipment_maintenance数据库
- **本地数据读取**: 自动从data文件夹读取设备传感器数据
- **可视化图表**: Plotly.js支持的交互式数据图表，完美匹配数据点和横轴刻度
- **智能工具选择**: 根据用户指定自动选择合适的诊断工具
- **错误恢复**: 自动回退机制，确保系统稳定运行
- **数据优化**: 智能数据限制，确保图表显示清晰准确
- **模块化设计**: 低耦合、高扩展性的Agent和工具架构

## 🚀 快速开始

### 环境要求
- Python 3.9+
- MongoDB (可选)
- DeepSeek API密钥 (可选，用于智能对话)

### 安装步骤

1. **创建虚拟环境**
```bash
conda create -n MAChat2 python=3.9 -y
conda activate MAChat2
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量** (可选)
创建`.env`文件：
```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
MONGODB_URI=mongodb://localhost:27017/
MONGODB_DB=equipment_maintenance
SECRET_KEY=your_secret_key_here
```

4. **启动应用**
```bash
python app.py
```
或使用智能启动脚本：
```bash
python start.py
```
或双击 `start.bat` (Windows)

5. **访问系统**
打开浏览器访问: http://127.0.0.1:5000

## 📁 项目结构

```
MAgent_V1.0/
├── 📄 app.py                    # Flask Web应用主文件
├── 📄 start.py                  # 智能启动脚本 (推荐)
├── 📄 start.bat                 # Windows快速启动脚本
├── 📄 requirements.txt          # Python依赖包列表
├── 📄 .env                      # 环境变量配置文件
├── 📄 .gitignore               # Git忽略文件配置
├── 📄 README.md                # 项目说明文档 (本文件)
├── 📄 DEEPSEEK_SETUP_GUIDE.md  # DeepSeek API配置指南
├── 📄 PROJECT_STRUCTURE.md     # 详细项目结构说明
├── 📄 demo_diagnosis_methods.py # 诊断方法演示脚本
│
├── 📂 agents_v2/               # 新版Agent系统 (基于DeepSeek API)
│   ├── 📄 __init__.py
│   ├── 📄 base_agent.py        # Agent基类定义
│   ├── 📄 coordinator.py       # 协调器基类
│   ├── 📄 deepseek_agent.py    # DeepSeek Agent基类 (核心)
│   ├── 📄 deepseek_coordinator.py  # DeepSeek协调器 (主协调器)
│   ├── 📄 simple_coordinator.py    # 简化协调器 (本地处理)
│   ├── 📄 data_agent.py        # 数据分析Agent
│   ├── 📄 diagnosis_agent.py   # 故障诊断Agent
│   └── 📄 knowledge_agent.py   # 知识推荐Agent
│
├── 📂 tools/                   # 工具模块 (LangChain工具)
│   ├── 📄 __init__.py
│   ├── 📄 data_tools.py        # 数据获取和分析工具 (设备数据、传感器数据)
│   ├── 📄 diagnosis_tools.py   # 传统故障诊断工具
│   ├── 📄 cnn_diagnosis_tools.py   # CNN深度学习诊断工具
│   ├── 📄 hybrid_diagnosis_tools.py # 混合诊断工具
│   ├── 📄 knowledge_tools.py   # 知识库工具 (搜索、推荐)
│   └── 📄 simple_data_tools.py # 简化数据工具 (无外部依赖)
│
├── 📂 database/                # 数据库模块
│   ├── 📄 __init__.py
│   └── 📄 mongodb.py           # MongoDB连接和操作
│
├── 📂 utils/                   # 工具函数
│   ├── 📄 __init__.py
│   └── 📄 llm_utils.py         # LLM相关工具函数
│
├── 📂 templates/               # Web模板
│   └── 📄 index.html           # 主页面模板 (单页应用)
│
├── 📂 data/                    # 数据文件
│   ├── 📂 fan_1/               # 1号风机数据
│   │   ├── 📂 bearing/         # 轴承传感器数据
│   │   ├── 📂 drive/           # 驱动传感器数据
│   │   └── 📂 gearbox/         # 齿轮箱传感器数据
│   ├── 📂 fan_2/               # 2号风机数据
│   ├── 📂 fan_3/               # 3号风机数据
│   ├── 📄 fault_patterns.json  # 故障模式数据库
│   └── 📄 knowledge_base.json  # 维护知识库
│
└── 📂 models/                  # AI模型文件 (自动生成)
    └── 📄 cnn_fault_diagnosis.h5  # CNN故障诊断模型
```

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        Web前端界面                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   聊天对话区    │  │   分析报告区    │  │   数据图表区    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Flask Web应用                              │
│                     (app.py - API路由)                         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        协调器层                                 │
│  ┌─────────────────────────┐  ┌─────────────────────────────┐   │
│  │   DeepSeek智能协调器    │  │      简化本地协调器         │   │
│  │  (deepseek_coordinator) │  │   (simple_coordinator)     │   │
│  │                         │  │                             │   │
│  │ • 智能Agent调度         │  │ • 规则基础处理              │   │
│  │ • 上下文管理           │  │ • 本地数据分析              │   │
│  │ • 结果融合             │  │ • 无API依赖                │   │
│  └─────────────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Agent层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  数据分析Agent  │  │  故障诊断Agent  │  │  知识推荐Agent  │ │
│  │  (data_agent)   │  │(diagnosis_agent)│  │(knowledge_agent)│ │
│  │                 │  │                 │  │                 │ │
│  │ • 传感器数据    │  │ • 传统诊断      │  │ • 知识搜索      │ │
│  │ • 设备状态      │  │ • CNN诊断       │  │ • 维护建议      │ │
│  │ • 趋势分析      │  │ • 混合诊断      │  │ • 操作指南      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        工具层                                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │  数据工具   │ │  诊断工具   │ │  CNN工具    │ │  知识工具   ││
│  │             │ │             │ │             │ │             ││
│  │• 设备数据   │ │• 故障模式   │ │• 深度学习   │ │• 知识搜索   ││
│  │• 传感器读取 │ │• 规则匹配   │ │• 模式识别   │ │• 内容推荐   ││
│  │• 数据分析   │ │• 诊断推理   │ │• 置信评估   │ │• 知识管理   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        数据层                                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │  本地文件   │ │  MongoDB    │ │  AI模型     │ │  DeepSeek   ││
│  │             │ │             │ │             │ │    API      ││
│  │• CSV数据    │ │• 设备信息   │ │• CNN模型    │ │• LLM推理    ││
│  │• JSON配置   │ │• 历史记录   │ │• 预训练权重 │ │• 自然语言   ││
│  │• 故障模式   │ │• 知识库     │ │• 特征提取   │ │• 智能对话   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 系统工作流程

### 1. 用户交互流程

```
用户输入查询
    │
    ▼
解析查询内容 ──────┐
    │             │
    ▼             ▼
提取设备信息    提取工具指定
    │             │
    ▼             │
选择协调器 ◄──────┘
    │
    ▼
┌─────────────────────────────────────┐
│            协调器处理                │
│                                     │
│  阶段1: 数据收集                    │
│  ├─ 调用数据分析Agent               │
│  ├─ 读取传感器数据                  │
│  └─ 分析设备状态                    │
│                                     │
│  阶段2: 故障诊断                    │
│  ├─ 调用故障诊断Agent               │
│  ├─ 根据用户指定选择诊断工具        │
│  └─ 生成诊断结果                    │
│                                     │
│  阶段3: 知识推荐                    │
│  ├─ 调用知识推荐Agent               │
│  ├─ 搜索相关维护知识                │
│  └─ 生成操作建议                    │
│                                     │
│  阶段4: 结果整合                    │
│  ├─ 融合各Agent结果                 │
│  ├─ 生成综合报告                    │
│  └─ 格式化输出                      │
└─────────────────────────────────────┘
    │
    ▼
返回结果给用户
    │
    ▼
前端展示 ──────┐
    │         │
    ▼         ▼
聊天界面    分析报告
```

## 🎯 使用指南

### 基本使用
1. 启动系统后，在浏览器中打开 http://127.0.0.1:5000
2. 选择协调器类型：
   - **DeepSeek智能协调器** (推荐): 基于AI的智能对话，支持复杂推理
   - **简化协调器**: 本地处理，基于规则，无需API
3. 在对话框中输入查询，系统会自动识别意图并生成相应工作流：
   - **故障诊断**: "3号风机驱动端异响可能原因？"
   - **健康评估**: "请评估2号风机轴承的健康状态"
   - **寿命预测**: "1号风机电机还能用多久？"
   - **综合分析**: "请对4号风机进行全面分析"
   - **指定工具**: "请使用CNN诊断fan_2轴承温度过高"

### 支持的工作流类型

#### 1. 故障诊断工作流
- **触发关键词**: 故障、诊断、异常、问题、异响、振动、温度过高等
- **执行流程**: 数据分析 → 故障诊断 → 知识推荐
- **输出结果**: 故障原因分析、严重程度评估、维修建议

#### 2. 健康评估工作流
- **触发关键词**: 健康、状态、评估、健康评估、设备状态等
- **执行流程**: 数据分析 → 健康评估
- **输出结果**: 健康评分、风险等级、维护建议
- **技术基础**: CAE(卷积自编码器)模型

#### 3. 寿命预测工作流
- **触发关键词**: 寿命、预测、剩余、使用寿命、更换、退化等
- **执行流程**: 数据分析 → 寿命预测
- **输出结果**: 剩余寿命、预计失效日期、维护计划
- **技术基础**: RNN(循环神经网络)模型

#### 4. 综合分析工作流
- **触发关键词**: 全面、综合、完整、详细、全面分析等
- **执行流程**: 数据分析 → 故障诊断 → 健康评估 → 寿命预测 → 知识推荐
- **输出结果**: 完整的设备状态报告

#### 5. 数据分析工作流
- **触发关键词**: 数据、数据分析、传感器、监测、参数等
- **执行流程**: 数据分析
- **输出结果**: 传感器数据统计、异常检测

#### 6. 知识搜索工作流
- **触发关键词**: 知识、搜索、查找、资料、文档、手册等
- **执行流程**: 知识推荐
- **输出结果**: 相关维护知识和操作指南

### 工具指定语法
- **CNN诊断**: "请使用CNN"、"深度学习方法"、"神经网络诊断"
- **传统诊断**: "传统诊断方法"、"规则诊断"、"传统方法"
- **模式匹配**: "模式匹配方法"、"故障模式识别"
- **混合诊断**: "混合诊断"、"综合诊断方法"

### 界面说明
- **左侧**: 聊天对话界面，显示AI分析结果和诊断过程
- **右侧**: 设备状态分析报告
  - 设备信息：显示查询的设备和组件信息，包含工作流类型和置信度
  - 可能原因：列出故障的可能原因和严重程度
  - 建议措施：提供具体的维修建议
  - 相关知识：显示相关的维护知识和操作指南
  - 健康评估：显示设备健康评分、状态和风险等级（基于CAE模型）
  - 寿命预测：显示剩余使用寿命、预计失效日期和紧急程度（基于RNN模型）
  - 传感器数据：实时显示设备传感器数据图表（48个数据点，完美匹配横轴刻度）

## � 数据流和接口

### 数据流向图

```
用户查询 → Web前端 → Flask API → 协调器选择
                                      │
                                      ▼
                            ┌─────────────────┐
                            │  DeepSeek协调器  │
                            └─────────────────┘
                                      │
                    ┌─────────────────┼─────────────────┐
                    ▼                 ▼                 ▼
            ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
            │数据分析Agent│   │故障诊断Agent│   │知识推荐Agent│
            └─────────────┘   └─────────────┘   └─────────────┘
                    │                 │                 │
                    ▼                 ▼                 ▼
            ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
            │  数据工具   │   │  诊断工具   │   │  知识工具   │
            └─────────────┘   └─────────────┘   └─────────────┘
                    │                 │                 │
                    ▼                 ▼                 ▼
            ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
            │  本地文件   │   │  AI模型     │   │  知识库     │
            │  MongoDB    │   │  规则库     │   │  MongoDB    │
            └─────────────┘   └─────────────┘   └─────────────┘
                    │                 │                 │
                    └─────────────────┼─────────────────┘
                                      ▼
                              结果整合和返回
                                      │
                                      ▼
                            Web前端展示 (JSON响应)
```

### 主要API接口

#### 1. 故障诊断接口
```http
POST /api/diagnose
Content-Type: application/json

{
    "query": "3号风机驱动端异响可能原因？请用传统诊断方法分析",
    "coordinator": "deepseek"  // 或 "simple"
}
```

**响应格式**:
```json
{
    "equipment_info": {
        "equipment_id": "fan_3",
        "component": "drive",
        "symptoms": ["异响"]
    },
    "data_analysis": {
        "agent_output": "数据分析结果...",
        "tool_results": {
            "SimpleDataTool": {
                "sensor_data": [...],
                "analysis": {...}
            }
        },
        "success": true
    },
    "diagnosis": {
        "agent_output": "故障诊断结果...",
        "tool_results": {
            "FaultDiagnosisTool": {
                "possible_causes": [...],
                "recommendations": [...]
            }
        },
        "success": true
    },
    "knowledge_recommendations": {
        "agent_output": "知识推荐结果...",
        "tool_results": {
            "KnowledgeSearchTool": {
                "relevant_entries": [...]
            }
        },
        "success": true
    },
    "coordination_summary": {
        "total_agents": 3,
        "successful_agents": 3,
        "failed_agents": 0,
        "processing_time": 15.23
    },
    "coordinator_used": "deepseek",
    "query": "原始查询",
    "success": true
}
```

#### 2. 协调器状态接口
```http
GET /api/coordinators
```

**响应**:
```json
{
    "available_coordinators": ["deepseek", "simple"],
    "default": "auto",
    "deepseek_configured": true,
    "deepseek_api": "available"
}
```

#### 3. 系统测试接口
```http
GET /api/test
```

### 数据格式规范

#### 传感器数据格式 (txt)
```txt
timestamp,temperature,vibration,noise_level,speed,current,power
2024-01-01 10:00:00,45.2,0.12,65.3,1500,10.2,15.1
2024-01-01 10:01:00,45.5,0.13,66.1,1502,10.3,15.2
...
```

#### 故障模式数据 (JSON)
```json
{
    "bearing_faults": [
        {
            "pattern_id": "bearing_wear",
            "symptoms": ["振动增大", "温度升高", "噪声异常"],
            "severity": "high",
            "recommendations": ["更换轴承", "检查润滑"]
        }
    ]
}
```

#### 知识库数据 (JSON)
```json
{
    "maintenance_guides": [
        {
            "title": "轴承维护指南",
            "component": "bearing",
            "content": "详细维护步骤...",
            "keywords": ["轴承", "润滑", "温度"]
        }
    ]
}
```

## �🔧 配置说明

### DeepSeek API配置
1. 获取DeepSeek API密钥
2. 在`.env`文件中设置`DEEPSEEK_API_KEY`
3. 重启应用即可使用智能对话功能

### 数据配置
- 传感器数据存放在`data/`文件夹下
- 支持CSV格式，包含timestamp、temperature、vibration、noise等字段
- 系统会自动读取对应设备的数据文件

### MongoDB配置 (可选)
- 设置`MONGODB_URI`和`MONGODB_DB`环境变量
- 用于存储设备信息和维护历史

## 🛠️ 开发和扩展指南

### 添加新设备
1. 在`data/`文件夹下创建设备文件夹（如`fan_4/`）
2. 添加组件子文件夹（如`bearing/`, `drive/`, `motor/`等）
3. 在组件文件夹中放入传感器数据文件`sensor_data.txt`
4. 数据格式参考现有设备的CSV格式

### 扩展新的Agent
1. 在`agents_v2/`中创建新的Agent类
2. 继承`DeepSeekAgent`基类
3. 实现必要的方法：
   ```python
   class CustomAgent(DeepSeekAgent):
       def __init__(self):
           tools = [CustomTool()]  # 定义Agent使用的工具
           super().__init__("CustomAgent", "Agent描述", tools)

       def get_system_prompt(self) -> str:
           return "Agent的系统提示词"
   ```

### 添加新的诊断工具
1. 在`tools/`中创建新的工具类
2. 继承LangChain的`BaseTool`
3. 实现工具逻辑：
   ```python
   class CustomDiagnosisTool(BaseTool):
       name: str = "custom_diagnosis"
       description: str = "自定义诊断工具"

       def _run(self, equipment_id: str, **kwargs) -> Dict[str, Any]:
           # 实现诊断逻辑
           return {"result": "诊断结果"}
   ```

### 集成新的AI模型
1. 在`tools/`中创建模型工具类
2. 实现模型加载和推理逻辑
3. 在相应的Agent中注册工具
4. 更新工具选择逻辑

### 自定义协调器
1. 在`agents_v2/`中创建协调器类
2. 继承`BaseCoordinator`或实现协调器接口
3. 在`app.py`中注册新协调器
4. 更新前端协调器选择选项


## 🤖 Agent调用流程详解

### DeepSeek智能协调器流程

```
用户查询: "3号风机驱动端异响可能原因？请用传统诊断方法分析"
    │
    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    1. 查询解析阶段                               │
│                                                                 │
│  DeepSeekMultiAgentCoordinator.process_query()                 │
│  ├─ 解析设备信息: equipment_id="fan_3", component="drive"      │
│  ├─ 提取症状: symptoms=["异响"]                                │
│  ├─ 提取工具指定: "请用传统诊断方法"                           │
│  └─ 构建设备信息字典                                           │
└─────────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    2. 数据收集阶段                               │
│                                                                 │
│  _execute_data_analysis_phase()                                │
│  ├─ 调用: DeepSeekDataAgent.process()                         │
│  │   ├─ 查询: "获取设备 fan_3 的数据，重点关注 drive 组件"    │
│  │   ├─ 调用工具: SimpleDataTool.get_equipment_data()         │
│  │   │   ├─ 读取: data/fan_3/drive/sensor_data.txt           │
│  │   │   ├─ 解析CSV数据: 10条传感器记录                      │
│  │   │   └─ 分析异常: 振动3.63mm/s, 噪声112.84dB            │
│  │   ├─ DeepSeek API调用: 生成数据分析报告                   │
│  │   └─ 返回: 中文分析结果 + 工具调用结果                    │
│  └─ 输出: 设备状态分析，发现振动和噪声异常                    │
└─────────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    3. 故障诊断阶段                               │
│                                                                 │
│  _execute_diagnosis_phase()                                    │
│  ├─ 构建诊断查询: "对设备 fan_3 的 drive 组件进行故障诊断，   │
│  │                 观察到的症状包括：异响。请用传统诊断方法"   │
│  ├─ 调用: DeepSeekDiagnosisAgent.process()                    │
│  │   ├─ 工具选择逻辑: _should_use_tool()                     │
│  │   │   ├─ 检测关键词: "传统诊断方法" → traditional         │
│  │   │   ├─ 选择工具: FaultDiagnosisTool ✓                  │
│  │   │   └─ 跳过工具: CNNFaultDiagnosisTool ✗               │
│  │   ├─ 调用工具: FaultDiagnosisTool._run()                  │
│  │   │   ├─ 症状匹配: "异响" + "drive" → 齿轮箱/联轴器问题  │
│  │   │   ├─ 严重程度评估: medium/high                       │
│  │   │   └─ 生成建议: 检查润滑、对中、紧固                  │
│  │   ├─ DeepSeek API调用: 生成诊断报告                      │
│  │   └─ 返回: 传统诊断结果 + 工具调用结果                   │
│  └─ 输出: 故障原因分析和维修建议                              │
└─────────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    4. 知识推荐阶段                               │
│                                                                 │
│  _execute_knowledge_phase()                                    │
│  ├─ 调用: DeepSeekKnowledgeAgent.process()                    │
│  │   ├─ 查询: "搜索关于 drive 组件 异响 问题的维护知识和建议" │
│  │   ├─ 调用工具: KnowledgeSearchTool._run()                  │
│  │   │   ├─ 搜索知识库: component="drive", symptom="异响"    │
│  │   │   ├─ 匹配条目: 传动系统维护、润滑管理等               │
│  │   │   └─ 返回相关知识条目                                 │
│  │   ├─ DeepSeek API调用: 生成知识推荐                       │
│  │   └─ 返回: 详细维护指南和操作建议                         │
│  └─ 输出: 维护要点、预防措施、实用技巧                        │
└─────────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    5. 结果整合阶段                               │
│                                                                 │
│  _integrate_results()                                          │
│  ├─ 收集各Agent结果                                            │
│  │   ├─ 数据分析: 设备状态、异常检测                          │
│  │   ├─ 故障诊断: 可能原因、严重程度、建议措施                │
│  │   └─ 知识推荐: 维护知识、操作指南                          │
│  ├─ 生成协调摘要                                              │
│  │   ├─ 成功Agent数: 3/3                                      │
│  │   ├─ 处理时间统计                                          │
│  │   └─ 整体状态评估                                          │
│  └─ 构建最终响应                                              │
│      ├─ equipment_info: 设备和组件信息                        │
│      ├─ data_analysis: 数据分析结果                           │
│      ├─ diagnosis: 故障诊断结果                               │
│      ├─ knowledge_recommendations: 知识推荐                   │
│      └─ coordination_summary: 协调摘要                        │
└─────────────────────────────────────────────────────────────────┘
    │
    ▼
返回给Web前端 → 用户界面展示
```

### Agent内部工作机制

#### 1. DeepSeekAgent基类 (deepseek_agent.py)

```python
class DeepSeekAgent:
    def process(self, input_data: str, context: Dict = None):
        """Agent处理流程"""
        # 1. 调用DeepSeek API生成初始响应
        llm_response = self._call_deepseek_api(input_data, context)

        # 2. 智能工具选择和调用
        tool_results = self._try_call_tools(input_data, context)

        # 3. 生成综合响应
        final_response = self._generate_comprehensive_response(
            llm_response, tool_results, input_data
        )

        return final_response

    def _should_use_tool(self, tool, input_data: str):
        """智能工具选择逻辑"""
        # 检查用户是否明确指定了工具类型
        if "传统诊断方法" in input_data:
            return "FaultDiagnosisTool" in tool.__class__.__name__
        elif "CNN" in input_data or "深度学习" in input_data:
            return "CNNFaultDiagnosisTool" in tool.__class__.__name__
        # ... 其他工具选择逻辑
```

#### 2. 工具调用机制

```python
def _call_tool(self, tool, input_data: str, context: Dict = None):
    """根据工具类型调用相应方法"""
    if "CNNFaultDiagnosisTool" in tool.__class__.__name__:
        # CNN诊断工具调用
        return tool._run(
            equipment_id=self._extract_equipment_id(input_data),
            component=self._extract_component(input_data),
            sensor_data=self._get_sensor_data_for_cnn(context)
        )
    elif "FaultDiagnosisTool" in tool.__class__.__name__:
        # 传统诊断工具调用
        return tool._run(
            equipment_id=self._extract_equipment_id(input_data),
            component=self._extract_component(input_data),
            symptoms=self._extract_symptoms(input_data)
        )
```

## 🧠 故障诊断方法

系统支持多种故障诊断方法，用户可以精确指定：

### 1. 传统模式匹配诊断
- **原理**: 基于预定义规则和故障模式库
- **优势**: 快速、可解释性强、稳定可靠
- **适用**: 常见故障、明确症状
- **触发**: "传统诊断方法"、"规则诊断"

### 2. CNN深度学习诊断
- **原理**: 基于卷积神经网络的时序数据分析
- **优势**: 能识别复杂模式、自动特征提取
- **适用**: 复杂故障、微弱信号、模式识别
- **触发**: "CNN诊断"、"深度学习方法"、"神经网络"
- **支持故障类型**:
  - 轴承故障：磨损、润滑不良、对中不良
  - 驱动系统：皮带松弛、齿轮磨损、联轴器问题
  - 电机故障：过载、绝缘故障
  - 振动问题：不平衡、共振

### 3. 混合诊断（推荐）
- **原理**: 结合传统方法和CNN诊断
- **优势**: 综合多种方法的优点，提高准确性
- **输出**: 融合结果、置信度评估、综合建议
- **触发**: "混合诊断"、"综合诊断方法"

### 4. 模式匹配诊断
- **原理**: 基于故障模式库的模式识别
- **优势**: 专门针对特定故障模式
- **适用**: 已知故障模式、历史故障复现
- **触发**: "模式匹配方法"、"故障模式识别"

### CNN模型特性
- **1D CNN**: 适用于时序传感器数据
- **模拟模式**: 无TensorFlow时自动降级
- **特征分析**: 温度、振动、噪声、速度、电流、功率
- **置信度评估**: 提供预测可信度和不确定性量化

## 📊 传感器数据可视化

### 数据显示优化
系统经过全面优化，确保传感器数据图表显示完美：

#### 数据处理流程
1. **数据获取**: 从本地CSV文件读取50条传感器数据
2. **数据排序**: 按时间戳升序排列，确保时间连续性
3. **数据限制**: 智能限制为前48个数据点，匹配横轴刻度
4. **数据验证**: 验证所有数据数组长度一致
5. **图表渲染**: 使用Plotly.js生成交互式图表

#### 技术特性
- **完美匹配**: 48个数据点精确对应48个横轴刻度
- **时间连续**: 30分钟间隔，覆盖约24小时数据
- **多传感器**: 同时显示温度、振动、噪声三种传感器数据
- **实时更新**: 根据查询动态加载对应设备数据
- **错误处理**: 自动处理数据缺失和格式异常

#### 图表功能
- **交互式**: 支持缩放、平移、悬停显示数值
- **多Y轴**: 不同传感器使用独立的Y轴刻度
- **颜色区分**: 温度(青色)、振动(红色)、噪声(蓝色)
- **图例显示**: 清晰的图例说明和单位标注

## 🔧 扩展开发指南

系统采用模块化设计，具有很好的可扩展性。如果您需要添加新的功能Agent或工具，请按照以下步骤进行：

### 添加新Agent的步骤

#### 1. 创建Agent类文件
在 `agents_v2/` 目录下创建新的Agent文件，例如 `your_agent.py`：

```python
"""
您的自定义Agent
"""
from typing import Dict, Any, List
from .base_deepseek_agent import DeepSeekAgent
from tools.your_tools import YourTool

class DeepSeekYourAgent(DeepSeekAgent):
    """您的自定义Agent"""

    def __init__(self):
        # 初始化您的工具
        tools = [YourTool()]

        super().__init__(
            name="DeepSeekYourAgent",
            description="您的Agent描述",
            tools=tools
        )

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """您的Agent的系统提示词"""

    def _should_use_tool(self, tool, input_data: str) -> bool:
        """判断是否应该使用特定工具"""
        # 实现您的工具选择逻辑
        return True

    def _call_tool(self, tool, input_data: str, context: Dict = None) -> Dict[str, Any]:
        """调用工具的统一接口"""
        # 实现您的工具调用逻辑
        return tool._run(input_data)
```

#### 2. 创建工具类文件
在 `tools/` 目录下创建新的工具文件，例如 `your_tools.py`：

```python
"""
您的自定义工具
"""
from typing import Dict, Any, List, Optional
from langchain.tools import BaseTool

class YourTool(BaseTool):
    """您的自定义工具"""

    name: str = "your_tool"
    description: str = "您的工具描述"

    def _run(self, input_data: str) -> Dict[str, Any]:
        """执行工具逻辑"""
        try:
            # 实现您的工具逻辑
            result = {
                "success": True,
                "data": "您的处理结果"
            }
            return result
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
```

#### 3. 注册Agent到工作流管理器
修改 `agents_v2/deepseek_coordinator.py` 文件，在 `__init__` 方法中添加：

```python
# 导入您的Agent
from .your_agent import DeepSeekYourAgent

# 在初始化方法中注册
self.workflow_manager.register_agent("your_agent_type", DeepSeekYourAgent)
```

#### 4. 添加工作流模式
修改 `agents_v2/workflow_manager.py` 文件，在 `_initialize_workflow_patterns` 方法中添加：

```python
"your_workflow": {
    "keywords": [
        "您的关键词1", "您的关键词2", "触发词"
    ],
    "agents": ["data", "your_agent_type"],  # 包含您的Agent类型
    "description": "您的工作流描述",
    "priority": 7  # 设置优先级
}
```

#### 5. 更新前端显示（可选）
如果需要在前端显示特定结果，修改 `templates/index.html`：

1. 在HTML表格中添加新行：
```html
<tr>
    <td>您的功能</td>
    <td id="yourFeature">-</td>
</tr>
```

2. 在JavaScript中添加处理函数：
```javascript
function updateYourFeature(data) {
    const yourFeature = document.getElementById('yourFeature');
    // 处理您的数据并更新显示
}
```

3. 在 `updateAnalysisReport` 函数中调用：
```javascript
// 更新您的功能显示
updateYourFeature(data);
```

### 需要修改的文件清单

添加新Agent和工具时，通常需要修改以下文件：

#### 必须修改的文件：
1. **`agents_v2/your_agent.py`** - 新建您的Agent类
2. **`tools/your_tools.py`** - 新建您的工具类
3. **`agents_v2/deepseek_coordinator.py`** - 注册新Agent
4. **`agents_v2/workflow_manager.py`** - 添加工作流模式

#### 可选修改的文件：
5. **`templates/index.html`** - 前端显示（如需要）
6. **`README.md`** - 更新文档说明
7. **`requirements.txt`** - 如果使用了新的依赖包

### 开发建议

#### 1. 保持低耦合
- Agent之间通过上下文传递数据，避免直接依赖
- 工具应该是独立的，可以单独测试
- 使用标准的输入输出格式

#### 2. 遵循命名规范
- Agent类名：`DeepSeek{功能}Agent`
- 工具类名：`{功能}Tool`
- 文件名：使用下划线分隔的小写字母

#### 3. 完善错误处理
- 所有工具都应该有try-catch错误处理
- 返回标准的成功/失败格式
- 提供有意义的错误信息

#### 4. 添加日志记录
- 使用print语句记录关键步骤
- 格式：`print(f"--您的日志信息")`
- 便于调试和监控

#### 5. 编写测试
- 为新的Agent和工具编写单元测试
- 测试正常情况和异常情况
- 确保与现有系统的兼容性

### 示例：添加设备监控Agent

假设您要添加一个设备监控Agent，步骤如下：

1. 创建 `agents_v2/monitoring_agent.py`
2. 创建 `tools/monitoring_tools.py`
3. 在 `deepseek_coordinator.py` 中注册：
   ```python
   self.workflow_manager.register_agent("monitoring", DeepSeekMonitoringAgent)
   ```
4. 在 `workflow_manager.py` 中添加工作流：
   ```python
   "device_monitoring": {
       "keywords": ["监控", "实时", "状态监测"],
       "agents": ["data", "monitoring"],
       "description": "设备监控工作流",
       "priority": 8
   }
   ```

通过以上步骤，您就可以成功扩展系统功能，添加自己的Agent和工具。系统的模块化设计确保了新功能可以无缝集成到现有架构中。

## 📝 更新日志

### v1.4 (当前版本)
- ✅ 新增健康评估功能（基于CAE模型）
- ✅ 新增寿命预测功能（基于RNN模型）
- ✅ 实现灵活工作流系统，根据用户输入自动生成工作流
- ✅ 支持6种工作流类型：故障诊断、健康评估、寿命预测、综合分析、数据分析、知识搜索
- ✅ 新增健康评估Agent和寿命预测Agent
- ✅ 新增CAE健康评估工具和RNN寿命预测工具
- ✅ 更新前端界面，支持健康评估和寿命预测结果显示
- ✅ 完善扩展开发指南，支持用户自定义Agent和工具
- ✅ 优化系统架构，提高可扩展性和模块化程度

### v1.3
- ✅ 修复传感器数据图表显示问题
- ✅ 优化数据点和横轴刻度匹配（48个数据点）
- ✅ 解决数据重复和混合问题
- ✅ 改进前端数据处理逻辑
- ✅ 增强数据一致性验证
- ✅ 优化Plotly图表配置

### v1.2
- ✅ 新增CNN深度学习故障诊断
- ✅ 实现混合诊断方法
- ✅ 支持多种故障类型识别
- ✅ 添加置信度评估和特征重要性分析

### v1.1
- ✅ 简化项目结构，移除原版Agent系统
- ✅ 优化前端界面，只保留两个协调器选项
- ✅ 完善文档和启动脚本

### v1.0
- ✅ 完整的多Agent协同系统
- ✅ DeepSeek API集成
- ✅ 本地数据读取功能
- ✅ 现代化Web界面
- ✅ 实时数据可视化
- ✅ 智能故障诊断
- ✅ 知识库推荐系统

## 🎯 未来规划

### 短期目标 (v1.5)
- [ ] 优化深度学习模型性能
- [ ] 添加模型训练和更新功能
- [ ] 实现用户权限管理
- [ ] 增加更多设备类型支持

### 中期目标 (v2.0)
- [ ] 实现分布式部署
- [ ] 添加实时数据流处理
- [ ] 集成更多AI模型
- [ ] 支持移动端应用

### 长期目标 (v3.0)
- [ ] 构建设备数字孪生
- [ ] 实现自主学习和优化
- [ ] 支持多语言界面
- [ ] 建立行业标准接口

---

**开发团队**: MAgent开发组
**技术支持**: 基于DeepSeek API和多Agent架构
**开源协议**: MIT License
**最后更新**: 2024年12月